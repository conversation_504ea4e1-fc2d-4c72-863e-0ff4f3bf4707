{"name": "memory-brush-frontend", "private": true, "version": "1.0.0", "description": "记忆画笔 - 认知艺术疗法游戏前端", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "fabric": "^5.3.0", "framer-motion": "^10.16.16", "zustand": "^4.4.7", "axios": "^1.6.2", "canvas-confetti": "^1.9.2", "react-color": "^2.19.3", "react-use-gesture": "^9.1.3", "lodash-es": "^4.17.21", "dayjs": "^1.11.10"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/fabric": "^5.3.7", "@types/react-color": "^3.0.9", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}}