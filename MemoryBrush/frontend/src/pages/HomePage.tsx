import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, Card, Row, Col, Typography, Space, Progress, Avatar, Divider } from "antd";
import { motion, AnimatePresence } from "framer-motion";
import {
    PlayCircleOutlined,
    TrophyOutlined,
    UserOutlined,
    SettingOutlined,
    PictureOutlined,
    StarOutlined,
    HeartOutlined,
    SmileOutlined,
    GiftOutlined,
    TeamOutlined,
    SafetyOutlined,
    BulbOutlined,
} from "@ant-design/icons";

const { Title, Paragraph, Text } = Typography;

const HomePage: React.FC = () => {
    const navigate = useNavigate();

    // 模拟用户进度数据
    const userProgress = {
        currentLevel: 2,
        totalLevels: 4,
        completedStages: 5,
        totalStages: 12,
        overallProgress: 42,
        todayDrawings: 3,
        weeklyGoal: 7,
        consecutiveDays: 5,
    };

    useEffect(() => {}, []);

    return (
        <div className="min-h-screen" style={{ background: 'transparent' }}>
            <div className="max-w-6xl mx-auto px-4 pt-8 pb-12">
                {/* 简化的大按钮区域 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="mb-16"
                >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        {/* 开始绘画 - 主要按钮 */}
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="bg-gradient-to-br from-orange-400 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48"
                                onClick={() => navigate("/game")}
                            >
                                <div className="text-center text-white h-full flex flex-col justify-center">
                                    <PlayCircleOutlined className="text-6xl mb-4" />
                                    <Title level={2} className="text-white mb-2">
                                        开始绘画
                                    </Title>
                                    <Text className="text-white text-xl opacity-90">让创意自由流淌</Text>
                                </div>
                            </Card>
                        </motion.div>

                        {/* 作品画廊 */}
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="bg-white border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48"
                                onClick={() => navigate("/gallery")}
                            >
                                <div className="text-center h-full flex flex-col justify-center">
                                    <PictureOutlined className="text-6xl text-purple-500 mb-4" />
                                    <Title level={2} className="text-gray-700 mb-2">
                                        作品画廊
                                    </Title>
                                    <Text className="text-gray-600 text-xl">欣赏美好作品</Text>
                                </div>
                            </Card>
                        </motion.div>
                    </div>
                </motion.div>

                {/* 简化的进度展示 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8 }}
                    className="mb-16"
                >
                    <Card className="bg-white shadow-soft border-0 max-w-4xl mx-auto">
                        <div className="text-center p-8">
                            <Title level={2} className="text-gray-700 mb-8">
                                <TrophyOutlined className="mr-3 text-yellow-500" />
                                您的学习进度
                            </Title>

                            {/* 简化的进度条 */}
                            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8">
                                <div className="flex justify-between items-center mb-6">
                                    <Text className="text-2xl text-gray-700">艺术之旅进度</Text>
                                    <Text className="text-4xl font-bold text-purple-600">
                                        {userProgress.overallProgress}%
                                    </Text>
                                </div>
                                <Progress
                                    percent={userProgress.overallProgress}
                                    strokeColor={{
                                        "0%": "#3b82f6",
                                        "50%": "#8b5cf6",
                                        "100%": "#ec4899",
                                    }}
                                    strokeWidth={20}
                                    className="mb-4"
                                />
                                <Text className="text-xl text-gray-600">每一步都是成长，每一画都是进步 ✨</Text>
                            </div>

                            {/* 简化的成就统计 */}
                            <Row gutter={24}>
                                <Col span={8}>
                                    <div className="text-center p-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl">
                                        <div className="text-5xl mb-3">🎨</div>
                                        <Text className="text-4xl font-bold text-orange-600 block">
                                            {userProgress.currentLevel}
                                        </Text>
                                        <Text className="text-xl text-gray-600">当前级别</Text>
                                    </div>
                                </Col>
                                <Col span={8}>
                                    <div className="text-center p-6 bg-gradient-to-br from-green-100 to-green-200 rounded-xl">
                                        <div className="text-5xl mb-3">⭐</div>
                                        <Text className="text-4xl font-bold text-green-600 block">
                                            {userProgress.completedStages}
                                        </Text>
                                        <Text className="text-xl text-gray-600">完成阶段</Text>
                                    </div>
                                </Col>
                                <Col span={8}>
                                    <div className="text-center p-6 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl">
                                        <div className="text-5xl mb-3">🔥</div>
                                        <Text className="text-4xl font-bold text-pink-600 block">
                                            {userProgress.consecutiveDays}
                                        </Text>
                                        <Text className="text-xl text-gray-600">连续天数</Text>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </Card>
                </motion.div>

                {/* 简化的其他功能 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="mb-16"
                >
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="text-center cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32"
                                onClick={() => navigate("/profile")}
                            >
                                <UserOutlined className="text-4xl text-blue-500 mb-2" />
                                <Text className="text-lg font-medium text-gray-700">个人档案</Text>
                            </Card>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="text-center cursor-pointer bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32"
                                onClick={() => navigate("/leaderboard")}
                            >
                                <TrophyOutlined className="text-4xl text-yellow-500 mb-2" />
                                <Text className="text-lg font-medium text-gray-700">排行榜</Text>
                            </Card>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="text-center cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32"
                                onClick={() => navigate("/achievements")}
                            >
                                <StarOutlined className="text-4xl text-purple-500 mb-2" />
                                <Text className="text-lg font-medium text-gray-700">成就</Text>
                            </Card>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Card
                                className="text-center cursor-pointer bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32"
                                onClick={() => navigate("/settings")}
                            >
                                <SettingOutlined className="text-4xl text-green-500 mb-2" />
                                <Text className="text-lg font-medium text-gray-700">设置</Text>
                            </Card>
                        </motion.div>
                    </div>
                </motion.div>

                {/* 简化的温馨结尾 */}
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 1.2 }}
                    className="text-center mt-16 py-12"
                >
                    <div className="max-w-3xl mx-auto">
                        <div className="bg-gradient-to-r from-orange-100 via-pink-100 to-purple-100 rounded-2xl p-8 shadow-soft">
                            <div className="flex justify-center mb-6">
                                <div className="w-16 h-16 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                                    <HeartOutlined className="text-2xl text-white" />
                                </div>
                            </div>

                            <Title level={2} className="text-3xl mb-4">
                                <span className="bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                                    让艺术点亮记忆，让创造温暖心灵
                                </span>
                            </Title>

                            <Text className="text-xl text-gray-700 mb-6">每一笔都是希望，每一画都是奇迹</Text>

                            <Text className="text-lg text-gray-600">奇迹创造者团队 · 用心陪伴您的艺术之旅 ✨</Text>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
};

export default HomePage;
