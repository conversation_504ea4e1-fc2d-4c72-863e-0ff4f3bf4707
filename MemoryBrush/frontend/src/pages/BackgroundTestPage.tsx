import React from 'react';
import { Button, Space, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const BackgroundTestPage: React.FC = () => {
  const navigate = useNavigate();

  const testLevels = [
    { id: 'L1-1', name: '第一关第一阶段', type: 'L1' },
    { id: 'L1-2', name: '第一关第二阶段', type: 'L1' },
    { id: 'L1-3', name: '第一关第三阶段', type: 'L1' },
    { id: 'L1-4', name: '第一关第四阶段', type: 'L1' },
    { id: 'L1-5', name: '第一关第五阶段', type: 'L1' },
    { id: 'L2-1', name: '第二关第一阶段 (统一背景)', type: 'other' },
    { id: 'L3-1', name: '第三关第一阶段 (统一背景)', type: 'other' },
    { id: 'L4-1', name: '第四关第一阶段 (统一背景)', type: 'other' },
  ];

  const handleLevelClick = (levelId: string) => {
    const [level, stage] = levelId.split('-');
    navigate(`/game/${level}/${stage}`);
  };

  return (
    <div className="min-h-screen pt-8" style={{ background: 'transparent' }}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <Title level={1} className="text-white" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
            背景图片测试
          </Title>
          <Text className="text-white text-lg" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
            点击下面的按钮测试不同关卡的背景图片切换
          </Text>
        </div>

        <div className="text-center">
          <Space direction="vertical" size="large">
            <Space wrap size="middle">
              {testLevels.map((level) => (
                <Button
                  key={level.id}
                  type={level.type === 'L1' ? 'primary' : 'default'}
                  size="large"
                  onClick={() => handleLevelClick(level.id)}
                  style={{
                    minWidth: '200px',
                    height: '50px',
                    fontSize: '16px',
                    backgroundColor: level.type === 'L1' ? 'rgba(139, 92, 246, 0.9)' : 'rgba(34, 197, 94, 0.9)',
                    borderColor: level.type === 'L1' ? 'rgba(139, 92, 246, 0.9)' : 'rgba(34, 197, 94, 0.9)',
                    color: 'white',
                    backdropFilter: 'blur(10px)',
                  }}
                >
                  {level.name}
                </Button>
              ))}
            </Space>

            <Button
              type="default"
              size="large"
              onClick={() => navigate('/')}
              style={{
                minWidth: '200px',
                height: '50px',
                fontSize: '16px',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
              }}
            >
              返回首页
            </Button>
          </Space>
        </div>

        <div className="mt-12 text-center">
          <div 
            className="inline-block p-6 rounded-lg"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
              maxWidth: '600px',
            }}
          >
            <Title level={3}>测试说明</Title>
            <Text>
              <strong>紫色按钮 (L1关卡)</strong>：使用各自目录下的background.jpg文件作为背景图片。
              <br />
              例如：L1-2关卡显示 /L1-2/background.jpg 作为背景。
              <br />
              <strong>绿色按钮 (其他关卡)</strong>：统一使用 /background.jpg 作为背景图片。
              <br />
              <strong>其他页面</strong>：轨迹记录、首页等非游戏页面也使用统一背景 /background.jpg。
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackgroundTestPage;
