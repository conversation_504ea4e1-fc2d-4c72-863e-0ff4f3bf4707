import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Button, Space } from 'antd'
import { motion } from 'framer-motion'
import { ArrowLeftOutlined, UserOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography

const ProfilePage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen" style={{ background: 'transparent' }}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="text-center p-12 shadow-soft">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <UserOutlined className="text-4xl text-white" />
              </div>
              
              <Title level={1} className="text-blue-600 mb-4">
                个人资料
              </Title>
              
              <Paragraph className="text-xl text-gray-600 max-w-2xl mx-auto">
                个人资料功能正在开发中，敬请期待！
                <br />
                我们正在为您打造个性化的用户体验。
              </Paragraph>
            </div>

            <Space size="large">
              <Button
                size="large"
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/')}
                className="h-12 px-8 text-lg"
              >
                返回首页
              </Button>
            </Space>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default ProfilePage
