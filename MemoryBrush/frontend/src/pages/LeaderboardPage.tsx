import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ton, Space } from 'antd'
import { motion } from 'framer-motion'
import { ArrowLeftOutlined, TrophyOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography

const LeaderboardPage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen" style={{ background: 'transparent' }}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="text-center p-12 shadow-soft">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrophyOutlined className="text-4xl text-white" />
              </div>
              
              <Title level={1} className="text-orange-600 mb-4">
                排行榜
              </Title>
              
              <Paragraph className="text-xl text-gray-600 max-w-2xl mx-auto">
                排行榜功能正在开发中，敬请期待！
                <br />
                我们正在为您打造激励性的成就展示系统。
              </Paragraph>
            </div>

            <Space size="large">
              <Button
                size="large"
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/')}
                className="h-12 px-8 text-lg"
              >
                返回首页
              </Button>
            </Space>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default LeaderboardPage
