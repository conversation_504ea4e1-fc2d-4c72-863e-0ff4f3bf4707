import React from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter } from "react-router-dom";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import App from "./App.tsx";
import "./styles/index.css";

// Ant Design 主题配置
const theme = {
    token: {
        colorPrimary: "#8b5cf6", // brush-500
        colorSuccess: "#10b981",
        colorWarning: "#f59e0b",
        colorError: "#ef4444",
        borderRadius: 8,
        fontSize: 16, // 适合老年人的较大字体
        fontFamily: "Inter, system-ui, sans-serif",
    },
    components: {
        Button: {
            fontSize: 18, // 按钮字体更大
            paddingContentHorizontal: 24,
            paddingContentVertical: 12,
        },
        Input: {
            fontSize: 16,
            paddingBlock: 12,
        },
        Card: {
            borderRadius: 12,
        },
    },
};

ReactDOM.createRoot(document.getElementById("root")!).render(
    <React.StrictMode>
        <BrowserRouter>
            <ConfigProvider
                locale={zhCN}
                theme={theme}
                componentSize="large" // 全局大尺寸组件
            >
                <App />
            </ConfigProvider>
        </BrowserRouter>
    </React.StrictMode>
);
