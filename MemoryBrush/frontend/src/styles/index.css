@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置和基础设置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-size: 16px; /* 基础字体大小，适合老年人 */
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    /* 背景图片将通过JavaScript动态设置 */
    background-size: auto 100vh; /* 高度填满视口，宽度自适应 */
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-color: #f0f0f0; /* 添加背景色填充空白区域 */
    min-height: 100vh;
    width: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 提高可访问性 - 更大的点击区域 */
  button, a, input, select, textarea {
    min-height: 44px; /* 符合无障碍设计的最小点击区域 */
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    body {
      background-color: #ffffff;
      color: #000000;
    }
  }

  /* 减少动画（如果用户偏好） */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* 组件样式 */
@layer components {
  /* 主要按钮样式 */
  .btn-primary {
    @apply bg-purple-500 hover:bg-purple-600 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-6 py-3 rounded-lg transition-colors duration-200;
  }

  .btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
  }

  /* 透明画布卡片 */
  .transparent-canvas-card {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  .transparent-canvas-card .ant-card-body {
    background-color: transparent !important;
    padding: 0 !important;
  }

  .card-hover {
    @apply card hover:shadow-md transition-shadow duration-200;
  }

  /* 画布容器 */
  .canvas-container {
    @apply relative rounded-lg border-2 border-gray-200 overflow-hidden;
    background-color: transparent !important;
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06) !important;
  }

  /* 工具栏样式 */
  .toolbar {
    @apply flex items-center gap-4 p-4 bg-white rounded-lg shadow-sm border border-gray-200;
  }

  /* 级别指示器 */
  .level-indicator {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .level-1 {
    @apply bg-green-100 text-green-800;
  }

  .level-2 {
    @apply bg-blue-100 text-blue-800;
  }

  .level-3 {
    @apply bg-purple-100 text-purple-800;
  }

  .level-4 {
    @apply bg-orange-100 text-orange-800;
  }

  /* 进度条样式 */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-3 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-purple-400 to-purple-600 transition-all duration-500 ease-out;
  }

  /* 成就徽章 */
  .achievement-badge {
    @apply inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full font-medium shadow-md;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500;
  }

  /* 提示气泡 */
  .tooltip {
    @apply absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
  }

  .tooltip.show {
    @apply opacity-100 pointer-events-auto;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本大小变体（适合老年人） */
  .text-elderly-sm {
    @apply text-lg; /* 18px */
  }

  .text-elderly-base {
    @apply text-xl; /* 20px */
  }

  .text-elderly-lg {
    @apply text-2xl; /* 24px */
  }

  .text-elderly-xl {
    @apply text-3xl; /* 30px */
  }

  /* 高对比度文本 */
  .text-high-contrast {
    @apply text-gray-900;
  }

  /* 无障碍隐藏（屏幕阅读器可见） */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* 焦点样式 */
  .focus-visible {
    @apply outline-none ring-2 ring-purple-500 ring-offset-2;
  }

  /* 触摸友好的间距 */
  .touch-spacing {
    @apply p-4 m-2;
  }

  /* 渐变背景 */
  .gradient-primary {
    @apply bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600;
  }

  .gradient-memory {
    @apply bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600;
  }

  /* 阴影变体 */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
}

/* Canvas 特定样式 */
.canvas-wrapper {
  touch-action: none; /* 防止触摸滚动干扰绘画 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 强制透明canvas */
.transparent-canvas {
  background-color: transparent !important;
  background: transparent !important;
}

/* 强制透明所有相关元素 */
.transparent-canvas-card,
.transparent-canvas-card *,
.canvas-container,
.canvas-container *,
canvas.transparent-canvas,
canvas {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* 特别针对canvas元素的强制透明 */
canvas[width][height] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 布局修复 */
.ant-layout {
  background: transparent !important;
  min-height: 100vh !important;
}

.ant-layout-content {
  background: transparent !important;
  min-height: 100vh !important;
}

.ant-layout-header {
  background: white !important;
  padding: 0 !important;
}

/* 确保Header内的Typography组件没有额外的margin */
.ant-layout-header .ant-typography {
  margin: 0 !important;
}

.ant-layout-footer {
  background: transparent !important;
  padding: 0 !important;
}

/* 确保所有容器都不会遮挡背景图片 */
#root {
  min-height: 100vh;
  background: transparent !important;
}

/* 确保页面内容容器透明 */
.min-h-screen {
  background: transparent !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
  .text-7xl {
    font-size: 3rem !important;
  }

  .text-6xl {
    font-size: 2.5rem !important;
  }

  .text-5xl {
    font-size: 2rem !important;
  }

  .text-4xl {
    font-size: 1.75rem !important;
  }

  .text-3xl {
    font-size: 1.5rem !important;
  }
}

/* 全屏样式 - 使用智能背景策略 */
:fullscreen {
  /* 背景图片将通过JavaScript动态设置 */
  background-size: auto 100%; /* 高度填满，宽度自适应，避免裁剪 */
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: #f0f0f0;
  width: 100vw;
  height: 100vh;
}

:-webkit-full-screen {
  /* 背景图片将通过JavaScript动态设置 */
  background-size: auto 100%; /* 高度填满，宽度自适应，避免裁剪 */
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: #f0f0f0;
  width: 100vw;
  height: 100vh;
}

:-moz-full-screen {
  /* 背景图片将通过JavaScript动态设置 */
  background-size: auto 100%; /* 高度填满，宽度自适应，避免裁剪 */
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: #f0f0f0;
  width: 100vw;
  height: 100vh;
}

/* 全屏提示模态框样式 */
.fullscreen-prompt-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.fullscreen-prompt-modal .ant-modal-body {
  padding: 0;
}

/* 浮动导航样式 */
.floating-nav-button {
  backdrop-filter: blur(10px);
  background: rgba(139, 92, 246, 0.9) !important;
}

.floating-nav-menu {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 增强阴影效果 */
.shadow-3xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* 增强背景图片显示 */
html {
  height: 100%;
  /* 背景图片将通过JavaScript动态设置 */
  background-size: auto 100vh; /* 高度填满视口，宽度自适应 */
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: #f0f0f0;
  overflow-x: hidden;
}

/* 确保根元素和body都有正确的背景设置 */
html, body {
  height: 100%;
  width: 100%;
  /* 背景图片将通过JavaScript动态设置 */
  background-size: auto 100vh; /* 高度填满视口，宽度自适应 */
  background-position: center center;
  background-repeat: no-repeat;
  background-color: #f0f0f0;
}

/* 统一背景策略：确保图片高度始终适应屏幕 */

/* 桌面设备：高度填满，宽度自适应 */
@media screen and (min-width: 769px) {
  html, body {
    background-size: auto 100vh !important; /* 高度填满视口，宽度自适应 */
    background-position: center center !important;
    background-color: #f0f0f0 !important;
    background-attachment: fixed !important;
  }
}

/* 移动设备：性能优化 */
@media screen and (max-width: 768px) {
  html, body {
    background-size: auto 100vh !important; /* 高度填满视口，宽度自适应 */
    background-position: center center !important;
    background-color: #f0f0f0 !important;
    background-attachment: scroll !important; /* 移动设备性能优化 */
  }
}

/* 高DPI设备：确保图片清晰 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  html, body {
    /* 背景图片将通过JavaScript动态设置，不在CSS中硬编码 */
    background-size: auto 100vh !important;
  }
}
