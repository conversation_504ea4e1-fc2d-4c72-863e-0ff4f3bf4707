import React, { useRef, useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Card, Typography, Button, Space, Slider, Switch, message } from "antd";
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, DownloadOutlined } from "@ant-design/icons";
import { GuidePath } from "../services/traceService";

const { Title, Text } = Typography;

interface GuidePreviewProps {
    guidePaths: GuidePath[];
    canvasSize: { width: number; height: number };
    backgroundImage?: string;
    onExport?: (guidePaths: GuidePath[]) => void;
}

const GuidePreview: React.FC<GuidePreviewProps> = ({ guidePaths, canvasSize, backgroundImage, onExport }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isAnimating, setIsAnimating] = useState(false);
    const [animationProgress, setAnimationProgress] = useState(0);
    const [animationSpeed, setAnimationSpeed] = useState(1);
    const [showAllPaths, setShowAllPaths] = useState(true);
    const [currentPathIndex, setCurrentPathIndex] = useState(0);
    const animationRef = useRef<number | null>(null);

    // 清空画布（预览时不显示背景图）
    const clearCanvas = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 预览时不绘制背景图，只显示纯净的引导线
    };

    // 绘制静态引导线
    const drawStaticGuidePaths = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        clearCanvas();

        if (showAllPaths) {
            // 绘制所有路径
            guidePaths.forEach((path, index) => {
                drawPath(ctx, path, index <= currentPathIndex);
            });
        } else {
            // 只绘制当前路径
            if (currentPathIndex < guidePaths.length) {
                drawPath(ctx, guidePaths[currentPathIndex], true);
            }
        }
    };

    // 绘制单个路径
    const drawPath = (ctx: CanvasRenderingContext2D, path: GuidePath, isActive: boolean) => {
        if (path.points.length < 2) return;

        ctx.strokeStyle = isActive ? "#FFD700" : "#E5E7EB"; // 金黄色或灰色
        ctx.lineWidth = isActive ? 3 : 2;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        // 设置虚线样式
        if (isActive) {
            ctx.setLineDash([10, 5]);
        } else {
            ctx.setLineDash([5, 5]);
        }

        ctx.beginPath();
        ctx.moveTo(path.points[0].x, path.points[0].y);

        for (let i = 1; i < path.points.length; i++) {
            ctx.lineTo(path.points[i].x, path.points[i].y);
        }

        ctx.stroke();
        ctx.setLineDash([]); // 重置虚线样式
    };

    // 绘制动画引导线
    const drawAnimatedGuidePaths = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        clearCanvas();

        if (showAllPaths) {
            // 绘制所有路径的动画
            guidePaths.forEach((path, index) => {
                if (index < currentPathIndex) {
                    // 已完成的路径
                    drawPath(ctx, path, true);
                } else if (index === currentPathIndex) {
                    // 当前正在绘制的路径
                    drawAnimatedPath(ctx, path, animationProgress);
                } else {
                    // 未开始的路径
                    drawPath(ctx, path, false);
                }
            });
        } else {
            // 只绘制当前路径的动画
            if (currentPathIndex < guidePaths.length) {
                drawAnimatedPath(ctx, guidePaths[currentPathIndex], animationProgress);
            }
        }
    };

    // 绘制动画路径
    const drawAnimatedPath = (ctx: CanvasRenderingContext2D, path: GuidePath, progress: number) => {
        if (path.points.length < 2) return;

        const totalPoints = path.points.length;
        const animatedPointCount = Math.floor(totalPoints * progress);

        ctx.strokeStyle = "#FFD700"; // 金黄色
        ctx.lineWidth = 3;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";
        ctx.setLineDash([10, 5]);

        ctx.beginPath();
        if (animatedPointCount > 0) {
            ctx.moveTo(path.points[0].x, path.points[0].y);

            // 绘制已完成的线段
            for (let i = 1; i < animatedPointCount; i++) {
                ctx.lineTo(path.points[i].x, path.points[i].y);
            }

            // 绘制部分完成的最后一段
            if (animatedPointCount < totalPoints) {
                const lastPoint = path.points[animatedPointCount - 1];
                const nextPoint = path.points[animatedPointCount];
                const segmentProgress = totalPoints * progress - animatedPointCount;

                const partialX = lastPoint.x + (nextPoint.x - lastPoint.x) * segmentProgress;
                const partialY = lastPoint.y + (nextPoint.y - lastPoint.y) * segmentProgress;

                ctx.lineTo(partialX, partialY);
            }
        }

        ctx.stroke();
        ctx.setLineDash([]);

        // 绘制箭头在动画末端
        if (progress > 0.1 && animatedPointCount > 0) {
            drawArrow(ctx, path, progress);
        }
    };

    // 绘制箭头（优化版本，减少抖动）
    const drawArrow = (ctx: CanvasRenderingContext2D, path: GuidePath, progress: number) => {
        const totalPoints = path.points.length;
        const animatedPointCount = Math.floor(totalPoints * progress);

        let arrowX, arrowY, directionX, directionY;

        if (animatedPointCount < totalPoints) {
            const lastPoint = path.points[animatedPointCount - 1];
            const nextPoint = path.points[animatedPointCount];
            const segmentProgress = totalPoints * progress - animatedPointCount;

            arrowX = lastPoint.x + (nextPoint.x - lastPoint.x) * segmentProgress;
            arrowY = lastPoint.y + (nextPoint.y - lastPoint.y) * segmentProgress;

            // 计算方向向量，使用更稳定的方法
            directionX = nextPoint.x - lastPoint.x;
            directionY = nextPoint.y - lastPoint.y;
        } else {
            arrowX = path.points[totalPoints - 1].x;
            arrowY = path.points[totalPoints - 1].y;

            // 使用最后几个点来计算更稳定的方向
            directionX = 0;
            directionY = 0;
            const lookBackPoints = Math.min(5, totalPoints - 1); // 向前看5个点或更少

            for (let i = 1; i <= lookBackPoints; i++) {
                const currentPoint = path.points[totalPoints - i];
                const prevPoint = path.points[totalPoints - i - 1];
                directionX += currentPoint.x - prevPoint.x;
                directionY += currentPoint.y - prevPoint.y;
            }

            // 平均化方向向量
            directionX /= lookBackPoints;
            directionY /= lookBackPoints;
        }

        // 标准化方向向量
        const length = Math.sqrt(directionX * directionX + directionY * directionY);
        if (length < 0.1) {
            // 如果方向向量太小，不绘制箭头
            return;
        }

        directionX /= length;
        directionY /= length;

        const angle = Math.atan2(directionY, directionX);
        const arrowSize = 15;

        ctx.fillStyle = "#FFD700"; // 金黄色
        ctx.strokeStyle = "#FFD700";
        ctx.lineWidth = 2;

        ctx.beginPath();
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(
            arrowX - arrowSize * Math.cos(angle - Math.PI / 6),
            arrowY - arrowSize * Math.sin(angle - Math.PI / 6)
        );
        ctx.lineTo(
            arrowX - arrowSize * Math.cos(angle + Math.PI / 6),
            arrowY - arrowSize * Math.sin(angle + Math.PI / 6)
        );
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    };

    // 动画循环
    const animate = () => {
        if (!isAnimating) return;

        setAnimationProgress(prev => {
            const newProgress = prev + 0.01 * animationSpeed;
            
            if (newProgress >= 1) {
                if (showAllPaths && currentPathIndex < guidePaths.length - 1) {
                    // 移动到下一个路径
                    setCurrentPathIndex(prev => prev + 1);
                    return 0;
                } else {
                    // 动画完成
                    setIsAnimating(false);
                    return 1;
                }
            }
            
            return newProgress;
        });

        animationRef.current = requestAnimationFrame(animate);
    };

    // 开始/暂停动画
    const toggleAnimation = () => {
        if (isAnimating) {
            setIsAnimating(false);
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        } else {
            setIsAnimating(true);
        }
    };

    // 重置动画
    const resetAnimation = () => {
        setIsAnimating(false);
        setAnimationProgress(0);
        setCurrentPathIndex(0);
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
        }
    };

    // 导出引导线数据
    const handleExport = () => {
        if (onExport) {
            onExport(guidePaths);
        } else {
            // 默认导出为JSON
            const dataStr = JSON.stringify({
                guidePaths,
                canvasSize,
                exportedAt: new Date().toISOString()
            }, null, 2);
            
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `guide_paths_${Date.now()}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            message.success("引导线数据已导出");
        }
    };

    // 动画效果
    useEffect(() => {
        if (isAnimating) {
            animationRef.current = requestAnimationFrame(animate);
        }
        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [isAnimating, animationSpeed, currentPathIndex, showAllPaths]);

    // 绘制效果
    useEffect(() => {
        if (isAnimating) {
            drawAnimatedGuidePaths();
        } else {
            drawStaticGuidePaths();
        }
    }, [guidePaths, animationProgress, currentPathIndex, showAllPaths, isAnimating]);

    if (guidePaths.length === 0) {
        return (
            <Card>
                <div className="text-center py-8">
                    <Text type="secondary">暂无引导线数据</Text>
                </div>
            </Card>
        );
    }

    return (
        <div className="guide-preview">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Card>
                    <div className="flex justify-between items-center mb-4">
                        <Title level={4}>引导线预览</Title>
                        <Space>
                            <Button
                                icon={isAnimating ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                                onClick={toggleAnimation}
                                type="primary"
                            >
                                {isAnimating ? "暂停" : "播放"}
                            </Button>
                            <Button
                                icon={<ReloadOutlined />}
                                onClick={resetAnimation}
                            >
                                重置
                            </Button>
                            <Button
                                icon={<DownloadOutlined />}
                                onClick={handleExport}
                            >
                                导出
                            </Button>
                        </Space>
                    </div>

                    {/* 控制面板 */}
                    <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                        <Space direction="vertical" className="w-full">
                            <div className="flex items-center justify-between">
                                <Text>动画速度:</Text>
                                <div className="w-48">
                                    <Slider
                                        min={0.1}
                                        max={3}
                                        step={0.1}
                                        value={animationSpeed}
                                        onChange={setAnimationSpeed}
                                        disabled={isAnimating}
                                    />
                                </div>
                            </div>
                            <div className="flex items-center justify-between">
                                <Text>显示所有路径:</Text>
                                <Switch
                                    checked={showAllPaths}
                                    onChange={setShowAllPaths}
                                    disabled={isAnimating}
                                />
                            </div>
                            <div className="flex items-center justify-between">
                                <Text>路径信息:</Text>
                                <Text type="secondary">
                                    共 {guidePaths.length} 条路径，当前第 {currentPathIndex + 1} 条
                                </Text>
                            </div>
                        </Space>
                    </div>

                    {/* 画布 */}
                    <div className="flex justify-center">
                        <canvas
                            ref={canvasRef}
                            width={canvasSize.width}
                            height={canvasSize.height}
                            className="border-2 border-gray-300 rounded-lg bg-white"
                            style={{
                                maxWidth: "100%",
                                maxHeight: "600px",
                            }}
                        />
                    </div>

                    {/* 统计信息 */}
                    <div className="mt-4 text-center">
                        <Space>
                            <Text type="secondary">画布尺寸: {canvasSize.width} × {canvasSize.height}</Text>
                            <Text type="secondary">路径数量: {guidePaths.length}</Text>
                            <Text type="secondary">
                                总点数: {guidePaths.reduce((sum, path) => sum + path.points.length, 0)}
                            </Text>
                        </Space>
                    </div>
                </Card>
            </motion.div>
        </div>
    );
};

export default GuidePreview;
