import React from 'react'
import { Spin, Typography } from 'antd'
import { motion } from 'framer-motion'
import { LoadingOutlined } from '@ant-design/icons'

const { Text } = Typography

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'large', 
  tip = '正在加载中...',
  className = ''
}) => {
  const customIcon = (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <LoadingOutlined style={{ fontSize: size === 'large' ? 48 : size === 'default' ? 32 : 24 }} />
    </motion.div>
  )

  return (
    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <Spin 
          indicator={customIcon} 
          size={size}
          className="mb-4"
        />
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Text className="text-lg text-gray-600 block mb-2">{tip}</Text>
          <Text className="text-sm text-gray-500">
            请稍候，我们正在为您准备最好的体验 ✨
          </Text>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default LoadingSpinner
