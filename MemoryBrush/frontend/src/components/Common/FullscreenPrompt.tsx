import React, { useState, useEffect } from 'react'
import { Modal, Button, Space } from 'antd'
import { FullscreenOutlined, CloseOutlined } from '@ant-design/icons'

interface FullscreenPromptProps {
  visible: boolean
  onClose: () => void
  onEnterFullscreen: () => void
}

const FullscreenPrompt: React.FC<FullscreenPromptProps> = ({
  visible,
  onClose,
  onEnterFullscreen
}) => {
  const handleEnterFullscreen = () => {
    onEnterFullscreen()
    onClose()
  }

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      centered
      width={500}
      closable={false}
      maskClosable={false}
      className="fullscreen-prompt-modal"
    >
      <div className="text-center p-6">
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <FullscreenOutlined className="text-3xl text-white" />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-800 mb-3">
            获得最佳体验
          </h2>
          
          <p className="text-lg text-gray-600 leading-relaxed">
            为了获得最佳的绘画体验，建议您使用全屏模式。
            <br />
            全屏模式可以让您专注于创作，减少干扰。
          </p>
        </div>

        <Space size="large" className="w-full justify-center">
          <Button
            type="primary"
            size="large"
            icon={<FullscreenOutlined />}
            onClick={handleEnterFullscreen}
            className="h-12 px-8 text-lg bg-gradient-to-r from-purple-500 to-purple-600 border-0"
          >
            进入全屏
          </Button>
          
          <Button
            size="large"
            icon={<CloseOutlined />}
            onClick={onClose}
            className="h-12 px-8 text-lg"
          >
            稍后再说
          </Button>
        </Space>

        <div className="mt-6 text-sm text-gray-500">
          <p>您也可以随时通过右上角的全屏按钮切换全屏模式</p>
        </div>
      </div>
    </Modal>
  )
}

export default FullscreenPrompt
