import React, { useState, useRef, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Button, Card, Typography, Space, Input, Select, message, Modal, Spin } from "antd";
import {
    PlayCircleOutlined,
    PauseCircleOutlined,
    StopOutlined,
    SaveOutlined,
    ClearOutlined,
    DownloadOutlined,
    EyeOutlined,
    PictureOutlined,
    DeleteOutlined
} from "@ant-design/icons";
import traceService, { TraceSession, TraceStroke, Point } from "../services/traceService";

const { Title, Text } = Typography;
const { Option } = Select;

const TraceRecorder: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isRecording, setIsRecording] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [isDrawing, setIsDrawing] = useState(false);
    const [currentStroke, setCurrentStroke] = useState<TraceStroke | null>(null);
    const [strokes, setStrokes] = useState<TraceStroke[]>([]);
    const [sessionName, setSessionName] = useState("");
    const [sessionDescription, setSessionDescription] = useState("");
    const [brushColor, setBrushColor] = useState("#000000");
    const [brushSize, setBrushSize] = useState(3);
    const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });
    const [backgroundImage, setBackgroundImage] = useState<string | null>(null);
    const [recordingStartTime, setRecordingStartTime] = useState<number | null>(null);
    const [pausedDuration, setPausedDuration] = useState(0);
    const [lastPauseTime, setLastPauseTime] = useState<number | null>(null);
    const [previewModalVisible, setPreviewModalVisible] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [backgroundImageFile, setBackgroundImageFile] = useState<File | null>(null);
    const [backgroundImagePreview, setBackgroundImagePreview] = useState<string | null>(null);
    const backgroundImageRef = useRef<HTMLImageElement | null>(null);

    // 绘制所有笔画的辅助函数
    const drawAllStrokes = useCallback((ctx: CanvasRenderingContext2D) => {
        strokes.forEach(stroke => {
            if (stroke.points.length < 2) return;

            // 使用默认样式绘制已保存的笔画
            ctx.strokeStyle = "#000000";
            ctx.lineWidth = 3;
            ctx.lineCap = "round";
            ctx.lineJoin = "round";
            ctx.beginPath();
            ctx.moveTo(stroke.points[0].x, stroke.points[0].y);

            for (let i = 1; i < stroke.points.length; i++) {
                ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
            }

            ctx.stroke();
        });
    }, [strokes]);

    // 绘制当前正在绘制的笔画
    const drawCurrentStroke = useCallback((ctx: CanvasRenderingContext2D, stroke: TraceStroke) => {
        if (stroke.points.length < 2) return;

        // 使用当前的画笔设置
        ctx.strokeStyle = brushColor;
        ctx.lineWidth = brushSize;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";
        ctx.beginPath();
        ctx.moveTo(stroke.points[0].x, stroke.points[0].y);

        for (let i = 1; i < stroke.points.length; i++) {
            ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
        }

        ctx.stroke();
    }, [brushColor, brushSize]);

    // 重绘所有笔画
    const redrawStrokes = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 先绘制背景图（如果有）
        if (backgroundImageRef.current) {
            ctx.drawImage(backgroundImageRef.current, 0, 0, canvas.width, canvas.height);
        }

        // 绘制所有已完成的笔画
        drawAllStrokes(ctx);

        // 绘制当前正在绘制的笔画
        if (currentStroke && currentStroke.points.length > 0) {
            drawCurrentStroke(ctx, currentStroke);
        }
    }, [strokes, drawAllStrokes, currentStroke, drawCurrentStroke]);

    // 预加载背景图
    useEffect(() => {
        if (backgroundImagePreview) {
            const img = new Image();
            img.onload = () => {
                backgroundImageRef.current = img;
                redrawStrokes(); // 背景图加载完成后重绘
            };
            img.src = backgroundImagePreview;
        } else {
            backgroundImageRef.current = null;
            redrawStrokes(); // 移除背景图后重绘
        }
    }, [backgroundImagePreview, redrawStrokes]);

    // 监听currentStroke变化，实时重绘
    useEffect(() => {
        redrawStrokes();
    }, [currentStroke, redrawStrokes]);

    // 初始化画布
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        // 确保Canvas尺寸正确设置
        canvas.width = canvasSize.width;
        canvas.height = canvasSize.height;

        // 设置Canvas的显示尺寸
        canvas.style.width = `${canvasSize.width}px`;
        canvas.style.height = `${canvasSize.height}px`;

        // 设置画布样式
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        // 处理触摸事件（非被动监听器）
        const handleTouchStart = (e: TouchEvent) => {
            if (!isRecording || isPaused) return;
            e.preventDefault();
            e.stopPropagation();

            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const pos = {
                x: (touch.clientX - rect.left) * scaleX,
                y: (touch.clientY - rect.top) * scaleY,
            };

            setIsDrawing(true);
            const now = Date.now();
            const newStroke: TraceStroke = {
                id: `stroke_${now}`,
                points: [pos]
            };
            setCurrentStroke(newStroke);
        };

        const handleTouchMove = (e: TouchEvent) => {
            if (!isDrawing || !currentStroke || !isRecording || isPaused) return;
            e.preventDefault();
            e.stopPropagation();

            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const pos = {
                x: (touch.clientX - rect.left) * scaleX,
                y: (touch.clientY - rect.top) * scaleY,
            };

            const updatedStroke = {
                ...currentStroke,
                points: [...currentStroke.points, pos]
            };
            setCurrentStroke(updatedStroke);
        };

        const handleTouchEnd = (e: TouchEvent) => {
            e.preventDefault();
            e.stopPropagation();

            if (!isDrawing || !currentStroke || !isRecording || isPaused) return;
            setIsDrawing(false);
            setStrokes(prev => [...prev, currentStroke]);
            setCurrentStroke(null);
        };

        // 添加非被动事件监听器
        canvas.addEventListener("touchstart", handleTouchStart, { passive: false });
        canvas.addEventListener("touchmove", handleTouchMove, { passive: false });
        canvas.addEventListener("touchend", handleTouchEnd, { passive: false });

        // 画布尺寸变化时重绘所有内容
        redrawStrokes();

        return () => {
            canvas.removeEventListener("touchstart", handleTouchStart);
            canvas.removeEventListener("touchmove", handleTouchMove);
            canvas.removeEventListener("touchend", handleTouchEnd);
        };
    }, [canvasSize, redrawStrokes, isRecording, isPaused, isDrawing, currentStroke]);

    // 清空画布（仅用于初始化和清空操作）
    const clearCanvas = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 如果有背景图片，重新绘制
        if (backgroundImageRef.current) {
            ctx.drawImage(backgroundImageRef.current, 0, 0, canvas.width, canvas.height);
        }
    }, []);




    // 获取鼠标位置
    const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas) return { x: 0, y: 0 };

        const rect = canvas.getBoundingClientRect();

        // 获取canvas的实际显示尺寸
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;

        // 计算相对于canvas的坐标，考虑缩放
        const x = (e.clientX - rect.left) * scaleX;
        const y = (e.clientY - rect.top) * scaleY;

        return { x, y };
    };

    // 开始绘制
    const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (!isRecording || isPaused) return;

        e.preventDefault();
        setIsDrawing(true);

        const pos = getMousePos(e);
        const now = Date.now();

        const newStroke: TraceStroke = {
            id: `stroke_${now}`,
            points: [pos]
        };

        setCurrentStroke(newStroke);
    };

    // 绘制过程
    const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (!isDrawing || !currentStroke || !isRecording || isPaused) return;

        e.preventDefault();
        const pos = getMousePos(e);

        const updatedStroke = {
            ...currentStroke,
            points: [...currentStroke.points, pos]
        };

        setCurrentStroke(updatedStroke);
    };

    // 结束绘制
    const handleMouseUp = () => {
        if (!isDrawing || !currentStroke) return;

        setIsDrawing(false);
        setStrokes(prev => [...prev, currentStroke]);
        setCurrentStroke(null);
    };

    // 开始录制
    const startRecording = () => {
        if (!sessionName.trim()) {
            message.error("请输入会话名称");
            return;
        }

        setIsRecording(true);
        setIsPaused(false);
        setRecordingStartTime(Date.now());
        setPausedDuration(0);
        setStrokes([]);
        clearCanvas();
        message.success("开始录制轨迹");
    };

    // 暂停/恢复录制
    const togglePause = () => {
        if (!isRecording) return;

        if (isPaused) {
            // 恢复录制
            if (lastPauseTime) {
                setPausedDuration(prev => prev + (Date.now() - lastPauseTime));
            }
            setLastPauseTime(null);
            setIsPaused(false);
            message.info("恢复录制");
        } else {
            // 暂停录制
            setLastPauseTime(Date.now());
            setIsPaused(true);
            setIsDrawing(false);
            setCurrentStroke(null);
            message.info("暂停录制");
        }
    };

    // 停止录制
    const stopRecording = () => {
        setIsRecording(false);
        setIsPaused(false);
        setIsDrawing(false);
        setCurrentStroke(null);
        
        if (lastPauseTime) {
            setPausedDuration(prev => prev + (Date.now() - lastPauseTime));
            setLastPauseTime(null);
        }
        
        message.success("录制已停止");
    };

    // 清空画布
    const clearAll = () => {
        setStrokes([]);
        clearCanvas();
        message.info("画布已清空");
    };



    // 优化轨迹数据：过滤短线和轻度优化路径
    const optimizeStrokes = (originalStrokes: TraceStroke[], minLengthPixels: number = 3): TraceStroke[] => {
        return originalStrokes.filter(stroke => {
            if (stroke.points.length < 2) {
                return false; // 过滤单点
            }

            // 计算笔画总长度
            let totalLength = 0;
            for (let i = 1; i < stroke.points.length; i++) {
                const dx = stroke.points[i].x - stroke.points[i-1].x;
                const dy = stroke.points[i].y - stroke.points[i-1].y;
                totalLength += Math.sqrt(dx * dx + dy * dy);
            }

            return totalLength >= minLengthPixels; // 过滤短线
        }).map(stroke => {
            // 只对较长的路径进行优化，保持短路径的原始细节
            if (stroke.points.length < 10) {
                return stroke; // 短路径保持原样
            }

            // 对长路径进行轻度平滑和简化
            const smoothedPoints = smoothStrokePoints(stroke.points);
            const simplifiedPoints = simplifyStrokePoints(smoothedPoints);

            return {
                ...stroke,
                points: simplifiedPoints
            };
        });
    };

    // 平滑路径点（轻度平滑，保持细节）
    const smoothStrokePoints = (points: Point[], windowSize: number = 2): Point[] => {
        if (points.length <= 3) {
            return points; // 对于很短的路径，不进行平滑
        }

        const smoothedPoints: Point[] = [];

        // 保留起点
        smoothedPoints.push(points[0]);

        // 对中间点进行轻度平滑
        for (let i = 1; i < points.length - 1; i++) {
            const prev = points[i - 1];
            const curr = points[i];
            const next = points[i + 1];

            // 使用加权平均，当前点权重更大
            const smoothedX = (prev.x + curr.x * 2 + next.x) / 4;
            const smoothedY = (prev.y + curr.y * 2 + next.y) / 4;

            smoothedPoints.push({
                x: smoothedX,
                y: smoothedY
            });
        }

        // 保留终点
        smoothedPoints.push(points[points.length - 1]);

        return smoothedPoints;
    };

    // 简化路径点（保守的简化，保持更多细节）
    const simplifyStrokePoints = (points: Point[], tolerance: number = 0.5): Point[] => {
        if (points.length <= 3) {
            return points; // 对于很短的路径，不进行简化
        }

        const simplified: Point[] = [points[0]]; // 保留起点

        for (let i = 1; i < points.length - 1; i++) {
            const prev = simplified[simplified.length - 1];
            const curr = points[i];

            // 计算当前点与前一个保留点的距离
            const distance = Math.sqrt(
                Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
            );

            // 如果距离大于容差，或者是重要的转折点，保留这个点
            if (distance > tolerance) {
                simplified.push(curr);
            }
        }

        simplified.push(points[points.length - 1]); // 保留终点
        return simplified;
    };

    // 计算点到线段的距离
    const pointToLineDistance = (point: Point, lineStart: Point, lineEnd: Point): number => {
        const A = point.x - lineStart.x;
        const B = point.y - lineStart.y;
        const C = lineEnd.x - lineStart.x;
        const D = lineEnd.y - lineStart.y;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) {
            return Math.sqrt(A * A + B * B);
        }

        const param = dot / lenSq;
        let xx, yy;

        if (param < 0) {
            xx = lineStart.x;
            yy = lineStart.y;
        } else if (param > 1) {
            xx = lineEnd.x;
            yy = lineEnd.y;
        } else {
            xx = lineStart.x + param * C;
            yy = lineStart.y + param * D;
        }

        const dx = point.x - xx;
        const dy = point.y - yy;
        return Math.sqrt(dx * dx + dy * dy);
    };

    // 保存轨迹数据
    const saveTraceData = async () => {
        if (strokes.length === 0) {
            message.error("没有轨迹数据可保存");
            return;
        }

        setIsSaving(true);

        try {
            const totalDuration = recordingStartTime ? Date.now() - recordingStartTime : 0;

            // 优化轨迹数据
            const optimizedStrokes = optimizeStrokes(strokes);
            console.log(`轨迹优化完成: 原始笔画数 ${strokes.length}, 优化后笔画数 ${optimizedStrokes.length}`);

            const sessionData = {
                name: sessionName,
                description: sessionDescription,
                strokes: optimizedStrokes,
                canvasSize: canvasSize,
                duration: totalDuration - pausedDuration
            };

            // 尝试保存到后端API
            try {
                const response = await traceService.createSession(sessionData);
                if (response.success && response.data) {
                    message.success("轨迹数据已保存到服务器");
                    console.log("保存成功，会话ID:", response.data.id);
                } else {
                    throw new Error(response.message);
                }
            } catch (apiError) {
                console.warn("API保存失败，使用本地存储:", apiError);

                // API失败时保存到本地存储
                const traceSession: TraceSession = {
                    id: `session_${Date.now()}`,
                    name: sessionName,
                    description: sessionDescription,
                    strokes: strokes,
                    canvasSize: canvasSize,
                    createdAt: new Date().toISOString(),
                    duration: totalDuration - pausedDuration
                };

                traceService.saveToLocalStorage(traceSession);
                message.success("轨迹数据已保存到本地");
            }

            // 重置状态
            setSessionName("");
            setSessionDescription("");
            setStrokes([]);
            clearCanvas();

        } catch (error) {
            console.error("保存失败:", error);
            message.error("保存失败，请重试");
        } finally {
            setIsSaving(false);
        }
    };

    // 处理背景图上传
    const handleBackgroundImageUpload = (file: File) => {
        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            message.error('请上传图片文件 (JPG, PNG, GIF, WebP)');
            return false;
        }

        // 验证文件大小 (10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            message.error('图片文件大小不能超过 10MB');
            return false;
        }

        setBackgroundImageFile(file);

        // 创建预览URL
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            setBackgroundImagePreview(imageUrl);

            // 根据图片尺寸调整画布大小
            const img = new Image();
            img.onload = () => {
                const maxWidth = Math.min(window.innerWidth * 0.8, 1200);
                const maxHeight = Math.min(window.innerHeight * 0.7, 800);

                let newWidth = img.width;
                let newHeight = img.height;

                // 保持宽高比，适应最大尺寸
                if (newWidth > maxWidth) {
                    newHeight = (newHeight * maxWidth) / newWidth;
                    newWidth = maxWidth;
                }

                if (newHeight > maxHeight) {
                    newWidth = (newWidth * maxHeight) / newHeight;
                    newHeight = maxHeight;
                }

                const newCanvasSize = {
                    width: Math.round(newWidth),
                    height: Math.round(newHeight)
                };
                setCanvasSize(newCanvasSize);

                message.success(`背景图已加载，画布尺寸调整为 ${Math.round(newWidth)} × ${Math.round(newHeight)}`);
            };
            img.src = imageUrl;
        };
        reader.readAsDataURL(file);

        return false; // 阻止默认上传行为
    };

    // 移除背景图
    const removeBackgroundImage = () => {
        setBackgroundImageFile(null);
        setBackgroundImagePreview(null);
        setBackgroundImage(null);
        setCanvasSize({ width: 800, height: 600 }); // 恢复默认尺寸
        message.info("背景图已移除");
    };

    // 清空所有内容（包括背景图）
    const clearAllIncludingBackground = () => {
        setStrokes([]);
        removeBackgroundImage();
        clearCanvas();
        message.info("画布和背景图已清空");
    };



    // 导出轨迹数据
    const exportTraceData = () => {
        if (strokes.length === 0) {
            message.error("没有轨迹数据可导出");
            return;
        }

        const totalDuration = recordingStartTime ? Date.now() - recordingStartTime : 0;

        // 优化轨迹数据
        const optimizedStrokes = optimizeStrokes(strokes);
        console.log(`导出轨迹优化完成: 原始笔画数 ${strokes.length}, 优化后笔画数 ${optimizedStrokes.length}`);

        const traceSession: TraceSession = {
            id: `session_${Date.now()}`,
            name: sessionName || "未命名会话",
            description: sessionDescription,
            strokes: optimizedStrokes,
            canvasSize: canvasSize,
            createdAt: new Date().toISOString(),
            duration: totalDuration - pausedDuration
        };

        traceService.exportSessionAsJson(traceSession);
        message.success("轨迹数据已导出");
    };

    return (
        <div className="trace-recorder min-h-screen p-3 bg-gray-50">
            <div className="max-w-7xl mx-auto">
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    {/* 主要工作区 */}
                    <div className="grid grid-cols-1 xl:grid-cols-4 gap-4">
                        {/* 左侧控制面板 */}
                        <div className="xl:col-span-1">
                            <Card size="small" className="mb-3">
                                <div className="space-y-4">
                                    {/* 会话信息 */}
                                    <div>
                                        <Text strong className="block mb-2">会话信息</Text>
                                        <Space direction="vertical" className="w-full" size="small">
                                            <Input
                                                placeholder="会话名称"
                                                value={sessionName}
                                                onChange={(e) => setSessionName(e.target.value)}
                                                disabled={isRecording}
                                                size="small"
                                            />
                                            <Input.TextArea
                                                placeholder="会话描述（可选）"
                                                value={sessionDescription}
                                                onChange={(e) => setSessionDescription(e.target.value)}
                                                disabled={isRecording}
                                                rows={2}
                                                size="small"
                                            />
                                        </Space>
                                    </div>



                                    {/* 背景图设置 */}
                                    <div>
                                        <Text strong className="block mb-2">背景图</Text>
                                        <Space direction="vertical" className="w-full" size="small">
                                            <Button
                                                icon={<PictureOutlined />}
                                                onClick={() => {
                                                    const input = document.createElement('input');
                                                    input.type = 'file';
                                                    input.accept = 'image/*';
                                                    input.onchange = (e) => {
                                                        const file = (e.target as HTMLInputElement).files?.[0];
                                                        if (file) {
                                                            handleBackgroundImageUpload(file);
                                                        }
                                                    };
                                                    input.click();
                                                }}
                                                disabled={isRecording}
                                                className="w-full"
                                                size="small"
                                            >
                                                上传背景图
                                            </Button>
                                            {backgroundImagePreview && (
                                                <div className="flex items-center justify-between">
                                                    <Text type="success" style={{ fontSize: '11px' }}>
                                                        ✓ 已加载
                                                    </Text>
                                                    <Button
                                                        icon={<DeleteOutlined />}
                                                        size="small"
                                                        danger
                                                        onClick={removeBackgroundImage}
                                                        disabled={isRecording}
                                                    >
                                                        移除
                                                    </Button>
                                                </div>
                                            )}
                                        </Space>
                                    </div>

                                    {/* 画笔设置 */}
                                    <div>
                                        <Text strong className="block mb-2">画笔设置</Text>
                                        <Space direction="vertical" className="w-full" size="small">
                                            <div className="flex items-center gap-2">
                                                <Text style={{ fontSize: '12px', width: '30px' }}>颜色</Text>
                                                <input
                                                    type="color"
                                                    value={brushColor}
                                                    onChange={(e) => setBrushColor(e.target.value)}
                                                    disabled={isRecording && !isPaused}
                                                    className="w-8 h-6 border rounded"
                                                />
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Text style={{ fontSize: '12px', width: '30px' }}>大小</Text>
                                                <Select
                                                    value={brushSize}
                                                    onChange={setBrushSize}
                                                    disabled={isRecording && !isPaused}
                                                    className="flex-1"
                                                    size="small"
                                                >
                                                    <Option value={1}>细 (1px)</Option>
                                                    <Option value={3}>中 (3px)</Option>
                                                    <Option value={5}>粗 (5px)</Option>
                                                    <Option value={8}>很粗 (8px)</Option>
                                                </Select>
                                            </div>
                                        </Space>
                                    </div>

                                    {/* 录制控制 */}
                                    <div>
                                <Title level={4}>录制控制</Title>
                                <Space wrap>
                                    {!isRecording ? (
                                        <Button
                                            type="primary"
                                            icon={<PlayCircleOutlined />}
                                            onClick={startRecording}
                                            size="large"
                                        >
                                            开始录制
                                        </Button>
                                    ) : (
                                        <>
                                            <Button
                                                icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                                                onClick={togglePause}
                                                size="large"
                                            >
                                                {isPaused ? "恢复" : "暂停"}
                                            </Button>
                                            <Button
                                                icon={<StopOutlined />}
                                                onClick={stopRecording}
                                                size="large"
                                                danger
                                            >
                                                停止
                                            </Button>
                                        </>
                                    )}
                                    <Button
                                        icon={<ClearOutlined />}
                                        onClick={clearAll}
                                        disabled={isRecording && !isPaused}
                                    >
                                        清空轨迹
                                    </Button>
                                    {backgroundImagePreview && (
                                        <Button
                                            icon={<DeleteOutlined />}
                                            onClick={clearAllIncludingBackground}
                                            disabled={isRecording && !isPaused}
                                            danger
                                        >
                                            清空全部
                                        </Button>
                                    )}
                                </Space>
                            </div>

                            {/* 保存和导出 */}
                            <div>
                                <Text strong className="block mb-2">保存操作</Text>
                                <Space direction="vertical" className="w-full" size="small">
                                    <Button
                                        type="primary"
                                        icon={<SaveOutlined />}
                                        onClick={saveTraceData}
                                        disabled={strokes.length === 0 || isSaving}
                                        loading={isSaving}
                                        className="w-full"
                                        size="small"
                                    >
                                        {isSaving ? "保存中..." : "保存轨迹"}
                                    </Button>
                                    <Button
                                        icon={<DownloadOutlined />}
                                        onClick={exportTraceData}
                                        disabled={strokes.length === 0}
                                        className="w-full"
                                        size="small"
                                    >
                                        导出JSON
                                    </Button>
                                    <Button
                                        icon={<EyeOutlined />}
                                        onClick={() => setPreviewModalVisible(true)}
                                        disabled={strokes.length === 0}
                                        className="w-full"
                                        size="small"
                                    >
                                        预览轨迹
                                    </Button>
                                </Space>
                            </div>

                            {/* 状态信息 */}
                            {isRecording && (
                                <div className="mt-4 p-2 bg-gray-100 rounded">
                                    <div className="flex items-center gap-2">
                                        <div className={`w-2 h-2 rounded-full ${isPaused ? 'bg-yellow-500' : 'bg-red-500 animate-pulse'}`}></div>
                                        <Text style={{ fontSize: '11px' }} strong>
                                            {isPaused ? "录制已暂停" : "正在录制..."}
                                        </Text>
                                    </div>
                                    <Text style={{ fontSize: '11px' }} type="secondary">
                                        笔画数: {strokes.length}
                                    </Text>
                                </div>
                            )}
                        </div>
                    </Card>
                </div>

                        {/* 画布区域 */}
                        <div className="xl:col-span-3">
                            <Card size="small">
                                <div className="flex flex-col items-center">
                                    {/* 画布信息 */}
                                    <div className="mb-2 text-center">
                                        <Text type="secondary" style={{ fontSize: '12px' }}>
                                            画布: {canvasSize.width} × {canvasSize.height}
                                            {backgroundImagePreview && " • 已加载背景图"}
                                        </Text>
                                    </div>

                                    {/* 画布 */}
                                    <div className="relative">
                                        <canvas
                                    ref={canvasRef}
                                    width={canvasSize.width}
                                    height={canvasSize.height}
                                    className={`border-2 border-gray-300 rounded-lg cursor-crosshair shadow-md ${
                                        backgroundImagePreview ? 'bg-transparent' : 'bg-white'
                                    }`}
                                    style={{
                                        touchAction: "none",
                                        userSelect: "none",
                                        maxWidth: "100%",
                                        maxHeight: "70vh",
                                        position: "relative",
                                        margin: 0,
                                        padding: 0,
                                        border: "none",
                                        outline: "none",
                                        display: "block",
                                    }}
                                    onMouseDown={handleMouseDown}
                                    onMouseMove={handleMouseMove}
                                    onMouseUp={handleMouseUp}
                                    onMouseLeave={() => setIsDrawing(false)}
                                />
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* 预览模态框 */}
            <Modal
                title="轨迹预览"
                open={previewModalVisible}
                onCancel={() => setPreviewModalVisible(false)}
                footer={null}
                width={900}
            >
                <div className="text-center">
                    <Text>会话: {sessionName || "未命名"}</Text>
                    <br />
                    <Text type="secondary">笔画数: {strokes.length}</Text>
                    <br />
                    <Text type="secondary">
                        录制时长: {Math.round((Date.now() - (recordingStartTime || Date.now()) - pausedDuration) / 1000)}秒
                    </Text>
                </div>
            </Modal>
        </div>
    );
};

export default TraceRecorder;
