import React, { useRef, useEffect, useState, useCallback } from "react";
import { Button, Space, Slider, ColorPicker, message } from "antd";
import { ClearOutlined, SaveOutlined } from "@ant-design/icons";

interface Point {
    x: number;
    y: number;
}

interface DrawingCanvasProps {
    width?: number;
    height?: number;
    onSave?: (imageData: string) => void;
    disabled?: boolean;
    backgroundImage?: string;
    showReference?: boolean;
    hideCanvas?: boolean;
    onClearRef?: (clearFn: () => void) => void;
    isCompleted?: boolean;
    onPathsChange?: (paths: Array<{ points: Point[]; color: string; size: number }>) => void;
}

const DrawingCanvas: React.FC<DrawingCanvasProps> = ({
    width = 800,
    height = 600,
    onSave,
    disabled = false,
    backgroundImage,
    showReference = false,
    hideCanvas = false,
    onClearRef,
    isCompleted: externalIsCompleted = false,
    onPathsChange,
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [brushSize, setBrushSize] = useState(5);
    const [brushColor, setBrushColor] = useState("#000000");
    const [paths, setPaths] = useState<
        Array<{
            points: Point[];
            color: string;
            size: number;
        }>
    >([]);
    const [currentPath, setCurrentPath] = useState<Point[]>([]);
    const [hasContent, setHasContent] = useState(false);
    const [isSaved, setIsSaved] = useState(false);

    // 使用外部传入的完成状态
    const isCompleted = externalIsCompleted;

    // 获取鼠标/触摸位置
    const getEventPos = useCallback((e: React.MouseEvent | React.TouchEvent) => {
        const canvas = canvasRef.current;
        if (!canvas) return { x: 0, y: 0 };

        const rect = canvas.getBoundingClientRect();

        // 获取canvas的实际显示尺寸
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;

        let clientX: number, clientY: number;

        if ("touches" in e) {
            // 触摸事件
            const touch = e.touches[0] || e.changedTouches[0];
            clientX = touch.clientX;
            clientY = touch.clientY;
        } else {
            // 鼠标事件
            clientX = e.clientX;
            clientY = e.clientY;
        }

        // 计算相对于canvas的坐标，考虑缩放
        const x = (clientX - rect.left) * scaleX;
        const y = (clientY - rect.top) * scaleY;

        return { x, y };
    }, []);

    // 开始绘画
    const startDrawing = useCallback(
        (e: React.MouseEvent | React.TouchEvent) => {
            if (disabled || isCompleted) return;

            e.preventDefault();
            setIsDrawing(true);
            const pos = getEventPos(e);
            setCurrentPath([pos]);
        },
        [disabled, isCompleted, getEventPos]
    );

    // 绘画中
    const draw = useCallback(
        (e: React.MouseEvent | React.TouchEvent) => {
            if (!isDrawing || disabled || isCompleted) return;

            e.preventDefault();
            const pos = getEventPos(e);
            setCurrentPath((prev) => [...prev, pos]);
        },
        [isDrawing, disabled, isCompleted, getEventPos]
    );

    // 结束绘画
    const stopDrawing = useCallback(() => {
        if (!isDrawing || disabled || isCompleted) return;

        setIsDrawing(false);
        if (currentPath.length > 1) {
            const newPath = {
                points: currentPath,
                color: brushColor,
                size: brushSize,
            };
            setPaths((prev) => {
                const newPaths = [...prev, newPath];
                setHasContent(true);
                setIsSaved(false); // 有新内容时标记为未保存
                // 通知父组件路径变化
                onPathsChange?.(newPaths);
                return newPaths;
            });
        }
        setCurrentPath([]);
    }, [isDrawing, disabled, isCompleted, currentPath, brushColor, brushSize]);

    // 绘制路径
    const drawPath = useCallback((ctx: CanvasRenderingContext2D, points: Point[], color: string, size: number) => {
        if (points.length < 2) return;

        ctx.strokeStyle = color;
        ctx.lineWidth = size;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);

        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }

        ctx.stroke();
    }, []);

    // 重绘画布
    const redrawCanvas = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d", { alpha: true });
        if (!ctx) return;

        // 强制设置canvas为透明
        canvas.style.backgroundColor = "rgba(0,0,0,0)";
        canvas.style.background = "none";

        // 清空画布，保持透明
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 确保没有白色背景被绘制
        ctx.globalCompositeOperation = "source-over";

        // 绘制背景图片
        if (backgroundImage && showReference) {
            const img = new Image();
            img.onload = () => {
                ctx.globalAlpha = 0.3;
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                ctx.globalAlpha = 1.0;

                // 重绘所有路径
                paths.forEach((path) => {
                    drawPath(ctx, path.points, path.color, path.size);
                });

                // 绘制当前路径
                if (currentPath.length > 1) {
                    drawPath(ctx, currentPath, brushColor, brushSize);
                }
            };
            img.src = backgroundImage;
        } else {
            // 绘制所有路径
            paths.forEach((path) => {
                drawPath(ctx, path.points, path.color, path.size);
            });

            // 绘制当前路径
            if (currentPath.length > 1) {
                drawPath(ctx, currentPath, brushColor, brushSize);
            }
        }
    }, [paths, currentPath, brushColor, brushSize, backgroundImage, showReference, drawPath]);

    // 清空画布
    const clearCanvas = useCallback(() => {
        // 立即清空画布的视觉内容，保持透明
        const canvas = canvasRef.current;
        if (canvas) {
            const ctx = canvas.getContext("2d", { alpha: true });
            if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
        }

        // 清空画布数据
        setPaths([]);
        setCurrentPath([]);
        setHasContent(false);
        setIsSaved(false); // 清空后重置保存状态
        // 通知父组件路径变化
        onPathsChange?.([]);
    }, [onPathsChange]);

    // 清空画布
    const handleClearCanvas = useCallback(() => {
        clearCanvas();
    }, [clearCanvas]);

    // 保存画作
    const saveDrawing = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const imageData = canvas.toDataURL("image/png");
        onSave?.(imageData);
        setIsSaved(true); // 标记为已保存
        message.success("画作已保存！");
    }, [onSave]);

    // 初始化canvas为透明
    useEffect(() => {
        const canvas = canvasRef.current;
        if (canvas) {
            const ctx = canvas.getContext("2d", { alpha: true });
            if (ctx) {
                // 确保canvas支持透明度
                ctx.globalCompositeOperation = "source-over";
                // 强制设置canvas样式为透明
                canvas.style.backgroundColor = "rgba(0,0,0,0)";
                canvas.style.background = "none";
                // 清空canvas并确保透明
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                // 设置canvas的CSS样式
                canvas.setAttribute(
                    "style",
                    "background-color: rgba(0,0,0,0) !important; background: none !important; touch-action: none; border-radius: 8px; display: block;"
                );

                // 额外确保透明度
                canvas.style.setProperty("background-color", "rgba(0,0,0,0)", "important");
                canvas.style.setProperty("background", "none", "important");

                console.log("Canvas initialized with alpha channel");
            }
        }
    }, []);

    // 重绘画布
    useEffect(() => {
        redrawCanvas();
    }, [redrawCanvas]);

    // 确保Canvas尺寸正确设置并添加触摸事件监听器
    useEffect(() => {
        const canvas = canvasRef.current;
        if (canvas) {
            // 设置Canvas的实际尺寸
            canvas.width = width;
            canvas.height = height;

            // 设置Canvas的显示尺寸
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;

            // 添加非被动触摸事件监听器
            const handleTouchStart = (e: TouchEvent) => {
                if (disabled || isCompleted) return;
                e.preventDefault();
                e.stopPropagation();

                const touch = e.touches[0];
                const rect = canvas.getBoundingClientRect();
                const scaleX = canvas.width / rect.width;
                const scaleY = canvas.height / rect.height;
                const pos = {
                    x: (touch.clientX - rect.left) * scaleX,
                    y: (touch.clientY - rect.top) * scaleY,
                };

                setIsDrawing(true);
                setCurrentPath([pos]);
            };

            const handleTouchMove = (e: TouchEvent) => {
                if (!isDrawing || disabled || isCompleted) return;
                e.preventDefault();
                e.stopPropagation();

                const touch = e.touches[0];
                const rect = canvas.getBoundingClientRect();
                const scaleX = canvas.width / rect.width;
                const scaleY = canvas.height / rect.height;
                const pos = {
                    x: (touch.clientX - rect.left) * scaleX,
                    y: (touch.clientY - rect.top) * scaleY,
                };

                setCurrentPath((prev) => [...prev, pos]);
            };

            const handleTouchEnd = (e: TouchEvent) => {
                e.preventDefault();
                e.stopPropagation();

                if (!isDrawing || disabled || isCompleted) return;
                setIsDrawing(false);
                if (currentPath.length > 1) {
                    const newPath = {
                        points: currentPath,
                        color: brushColor,
                        size: brushSize,
                    };
                    setPaths((prev) => {
                        const newPaths = [...prev, newPath];
                        setHasContent(true);
                        setIsSaved(false);
                        onPathsChange?.(newPaths);
                        return newPaths;
                    });
                }
                setCurrentPath([]);
            };

            canvas.addEventListener("touchstart", handleTouchStart, { passive: false });
            canvas.addEventListener("touchmove", handleTouchMove, { passive: false });
            canvas.addEventListener("touchend", handleTouchEnd, { passive: false });

            // 重绘
            redrawCanvas();

            return () => {
                canvas.removeEventListener("touchstart", handleTouchStart);
                canvas.removeEventListener("touchmove", handleTouchMove);
                canvas.removeEventListener("touchend", handleTouchEnd);
            };
        }
    }, [width, height, redrawCanvas, disabled, isCompleted, isDrawing, currentPath, brushColor, brushSize, onPathsChange]);

    // 将清空函数传递给父组件
    useEffect(() => {
        if (onClearRef) {
            onClearRef(clearCanvas);
        }
    }, [onClearRef, clearCanvas]);

    // 监听hideCanvas变化，当画布重新显示时确保清空
    useEffect(() => {
        if (!hideCanvas) {
            // 画布重新显示时，如果paths为空，强制清空画布保持透明
            if (paths.length === 0) {
                const canvas = canvasRef.current;
                if (canvas) {
                    const ctx = canvas.getContext("2d", { alpha: true });
                    if (ctx) {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    }
                }
            }
        }
    }, [hideCanvas, paths]);

    return (
        <div className="flex flex-col items-center gap-4" style={{ backgroundColor: "transparent" }}>
            {/* 工具栏 */}
            <div className="flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm border">
                <Space>
                    <span className="text-sm font-medium">画笔大小:</span>
                    <Slider
                        min={1}
                        max={20}
                        value={brushSize}
                        onChange={setBrushSize}
                        style={{ width: 100 }}
                        disabled={isCompleted}
                    />
                    <span className="text-sm text-gray-500">{brushSize}px</span>
                </Space>

                <Space>
                    <span className="text-sm font-medium">颜色:</span>
                    <ColorPicker
                        value={brushColor}
                        onChange={(color) => setBrushColor(color.toHexString())}
                        showText
                        disabled={isCompleted}
                    />
                </Space>

                <Space>
                    <Button
                        icon={<ClearOutlined />}
                        onClick={handleClearCanvas}
                        disabled={!hasContent || isSaved || isCompleted}
                        title="清空"
                    >
                        清空
                    </Button>

                    <Button
                        icon={<SaveOutlined />}
                        onClick={saveDrawing}
                        type="primary"
                        disabled={!hasContent || isSaved || isCompleted}
                        title="保存"
                    >
                        保存
                    </Button>
                </Space>
            </div>

            {/* 画布 */}
            {!hideCanvas && (
                <div
                    style={{
                        backgroundColor: "rgba(0,0,0,0)",
                        background: "none",
                        position: "relative",
                        borderRadius: "8px",
                        border: "2px solid #e5e7eb",
                        overflow: "hidden",
                    }}
                >
                    <canvas
                        ref={canvasRef}
                        width={width}
                        height={height}
                        className={isCompleted ? "cursor-not-allowed opacity-60" : "cursor-crosshair"}
                        onMouseDown={startDrawing}
                        onMouseMove={draw}
                        onMouseUp={stopDrawing}
                        onMouseLeave={stopDrawing}
                        style={{
                            touchAction: "none",
                            backgroundColor: "rgba(0,0,0,0)",
                            background: "none",
                            borderRadius: "8px",
                            display: "block",
                            opacity: "1",
                            position: "relative",
                            margin: 0,
                            padding: 0,
                            border: "none",
                            outline: "none",
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default DrawingCanvas;
