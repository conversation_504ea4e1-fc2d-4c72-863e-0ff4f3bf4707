import React, { useState, useEffect } from 'react'
import { Card, Typography, Button, Space, Progress, message } from 'antd'
import { motion } from 'framer-motion'
import { ArrowLeftOutlined, CheckOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons'
import DrawingCanvas from './DrawingCanvas'

const { Title, Text } = Typography

interface TracingGameProps {
  onBack: () => void
  onComplete: (score: number) => void
}

// 预定义的简单图案
const patterns = [
  {
    id: 'circle',
    name: '圆形练习',
    description: '跟随虚线描绘一个完整的圆形',
    svg: `<svg width="800" height="600" viewBox="0 0 800 600">
      <circle cx="400" cy="300" r="120" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
    </svg>`,
    difficulty: 1
  },
  {
    id: 'wave',
    name: '波浪线',
    description: '跟随波浪线条，练习流畅的曲线绘制',
    svg: `<svg width="800" height="600" viewBox="0 0 800 600">
      <path d="M50 300 Q200 200 350 300 T650 300" stroke="#e0e0e0" stroke-width="8" fill="none" stroke-dasharray="10,5"/>
    </svg>`,
    difficulty: 1
  },
  {
    id: 'zigzag',
    name: '锯齿线',
    description: '绘制锯齿状线条，提高手部控制精度',
    svg: `<svg width="800" height="600" viewBox="0 0 800 600">
      <path d="M50 350 L150 250 L250 350 L350 250 L450 350 L550 250 L650 350" stroke="#e0e0e0" stroke-width="8" fill="none" stroke-dasharray="10,5"/>
    </svg>`,
    difficulty: 1
  },
  {
    id: 'spiral',
    name: '螺旋线',
    description: '绘制螺旋线条，练习连续的圆弧动作',
    svg: `<svg width="800" height="600" viewBox="0 0 800 600">
      <path d="M400 300 m-50 0 a50 50 0 1 1 100 0 a75 75 0 1 1 -150 0 a100 100 0 1 1 200 0 a125 125 0 1 1 -250 0" stroke="#e0e0e0" stroke-width="8" fill="none" stroke-dasharray="10,5"/>
    </svg>`,
    difficulty: 2
  },
  {
    id: 'flower',
    name: '花朵轮廓',
    description: '描绘简单的花朵轮廓，练习组合线条',
    svg: `<svg width="800" height="600" viewBox="0 0 800 600">
      <circle cx="400" cy="300" r="50" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="400" cy="230" rx="35" ry="55" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="470" cy="255" rx="35" ry="55" transform="rotate(60 470 255)" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="470" cy="345" rx="35" ry="55" transform="rotate(120 470 345)" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="400" cy="370" rx="35" ry="55" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="330" cy="345" rx="35" ry="55" transform="rotate(-120 330 345)" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
      <ellipse cx="330" cy="255" rx="35" ry="55" transform="rotate(-60 330 255)" stroke="#e0e0e0" stroke-width="6" fill="none" stroke-dasharray="8,4"/>
    </svg>`,
    difficulty: 3
  }
]

const TracingGame: React.FC<TracingGameProps> = ({ onBack, onComplete }) => {
  const [currentPatternIndex, setCurrentPatternIndex] = useState(0)
  const [showReference, setShowReference] = useState(true)
  const [isCompleted, setIsCompleted] = useState(false)
  const [progress, setProgress] = useState(0)
  const [backgroundImage, setBackgroundImage] = useState<string>('')

  const currentPattern = patterns[currentPatternIndex]

  // 生成背景图片
  useEffect(() => {
    const svgBlob = new Blob([currentPattern.svg], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(svgBlob)
    setBackgroundImage(url)

    return () => {
      URL.revokeObjectURL(url)
    }
  }, [currentPattern])

  const handleSave = (imageData: string) => {
    // 简单的完成度评估（实际项目中可以实现更复杂的算法）
    const completionScore = Math.floor(Math.random() * 30) + 70 // 70-100分
    setProgress(completionScore)
    setIsCompleted(true)
    message.success(`太棒了！完成度：${completionScore}%`)
  }

  const handleComplete = () => {
    onComplete(progress)
  }

  const handleNextPattern = () => {
    if (currentPatternIndex < patterns.length - 1) {
      setCurrentPatternIndex(prev => prev + 1)
      setIsCompleted(false)
      setProgress(0)
    } else {
      handleComplete()
    }
  }

  const handleRestart = () => {
    setIsCompleted(false)
    setProgress(0)
  }

  const toggleReference = () => {
    setShowReference(!showReference)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* 标题区域 */}
          <Card className="mb-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Title level={2} className="mb-2 text-purple-600">
                  匀速直线
                </Title>
                <Text className="text-lg text-gray-600">
                  跟随引导线条，提高绘画精准度
                </Text>
              </div>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={onBack}
                size="large"
                className="h-12 px-6"
              >
                返回
              </Button>
            </div>
          </Card>

          {/* 当前图案信息 */}
          <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <Title level={4} className="text-blue-600 mb-2">
                  {currentPattern.name} ({currentPatternIndex + 1}/{patterns.length})
                </Title>
                <Text className="text-gray-700">
                  {currentPattern.description}
                </Text>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  icon={<EyeOutlined />}
                  onClick={toggleReference}
                  type={showReference ? 'primary' : 'default'}
                >
                  {showReference ? '隐藏' : '显示'}参考线
                </Button>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  currentPattern.difficulty === 1 ? 'bg-green-100 text-green-600' :
                  currentPattern.difficulty === 2 ? 'bg-blue-100 text-blue-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  难度: {currentPattern.difficulty}/3
                </div>
              </div>
            </div>
          </Card>

          {/* 绘画指导 */}
          <Card className="mb-6 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
            <div className="text-center">
              <Title level={4} className="text-orange-600 mb-3">
                📝 描线技巧
              </Title>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700">
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                  <span>慢慢描绘，不要急躁</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                  <span>尽量贴近参考线</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                  <span>保持线条连贯</span>
                </div>
              </div>
            </div>
          </Card>

          {/* 绘画区域 */}
          <div className="transparent-canvas-card">
            <DrawingCanvas
              width={800}
              height={600}
              onSave={handleSave}
              backgroundImage={backgroundImage}
              showReference={showReference}
            />
          </div>

          {/* 完成区域 */}
          {isCompleted && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mt-6"
            >
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckOutlined className="text-2xl text-white" />
                  </div>
                  
                  <Title level={3} className="text-green-600 mb-3">
                    🎉 描线完成！
                  </Title>
                  
                  <div className="mb-4">
                    <Text className="text-lg text-gray-700 block mb-2">
                      完成度评分
                    </Text>
                    <Progress 
                      percent={progress} 
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                      className="max-w-md mx-auto"
                    />
                  </div>

                  <Space size="large">
                    {currentPatternIndex < patterns.length - 1 ? (
                      <Button
                        type="primary"
                        size="large"
                        icon={<CheckOutlined />}
                        onClick={handleNextPattern}
                        className="h-12 px-8 bg-blue-500 hover:bg-blue-600"
                      >
                        下一个图案
                      </Button>
                    ) : (
                      <Button
                        type="primary"
                        size="large"
                        icon={<CheckOutlined />}
                        onClick={handleComplete}
                        className="h-12 px-8 bg-green-500 hover:bg-green-600"
                      >
                        完成练习
                      </Button>
                    )}
                    
                    <Button
                      size="large"
                      icon={<ReloadOutlined />}
                      onClick={handleRestart}
                      className="h-12 px-8"
                    >
                      重新描绘
                    </Button>
                  </Space>
                </div>
              </Card>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default TracingGame
