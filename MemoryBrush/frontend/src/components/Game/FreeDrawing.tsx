import React, { useState } from "react";
import { <PERSON>, <PERSON>po<PERSON>, Button, Space, message, Spin, Image } from "antd";
import { motion } from "framer-motion";
import { ArrowLeftOutlined, CheckOutlined, ReloadOutlined, PictureOutlined } from "@ant-design/icons";
import DrawingCanvas from "./DrawingCanvas";
import { gameService, LineAnalysisResult, DrawingPath } from "../../services/gameService";

const { Title, Text } = Typography;

interface FreeDrawingProps {
    onBack: () => void;
    onComplete: () => void;
}

const FreeDrawing: React.FC<FreeDrawingProps> = ({ onBack, onComplete }) => {
    const [isCompleted, setIsCompleted] = useState(false);
    const [savedImage, setSavedImage] = useState<string>("");
    const [clearCanvas, setClearCanvas] = useState<(() => void) | null>(null);
    const [analysisResult, setAnalysisResult] = useState<LineAnalysisResult | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [drawingPaths, setDrawingPaths] = useState<DrawingPath[]>([]);
    const [canvasKey, setCanvasKey] = useState(0); // 用于强制重新创建画布

    const handleSave = async (imageData: string) => {
        setSavedImage(imageData);
        setIsAnalyzing(true);

        try {
            // 第一步：快速分析线条和风格
            const result = await gameService.analyzeLines({
                canvas_data: imageData,
                paths: drawingPaths,
            });

            setAnalysisResult(result);
            setIsCompleted(true);
            setIsAnalyzing(false);
            message.success("风格分析完成！AI正在为您创作艺术作品...");

            // 第二步：异步生成AI图片
            if (result.ai_generated_image?.status === "generating") {
                try {
                    const aiResult = await gameService.generateAIImage({
                        canvas_data: imageData,
                        paths: drawingPaths,
                    });

                    // 更新分析结果中的AI图片信息
                    setAnalysisResult((prev) =>
                        prev
                            ? {
                                  ...prev,
                                  ai_generated_image: aiResult,
                              }
                            : prev
                    );

                    if (aiResult.status === "success") {
                        message.success("AI艺术作品生成完成！");
                    } else {
                        message.warning("AI生图暂时不可用，但风格分析已完成");
                    }
                } catch (aiError) {
                    console.error("AI生图失败:", aiError);
                    // 更新状态为失败
                    setAnalysisResult((prev) =>
                        prev
                            ? {
                                  ...prev,
                                  ai_generated_image: {
                                      status: "failed",
                                      error: "AI生图服务暂时不可用",
                                  },
                              }
                            : prev
                    );
                    message.warning("AI生图暂时不可用，但风格分析已完成");
                }
            }
        } catch (error) {
            console.error("线条分析失败:", error);
            message.error("分析失败，但作品已保存");
            setIsCompleted(true);
            setIsAnalyzing(false);
        }
    };

    const handleComplete = () => {
        if (savedImage) {
            onComplete();
        } else {
            message.warning("请先保存您的作品");
        }
    };

    const handleRestart = () => {
        setIsCompleted(false);
        setSavedImage("");
        setAnalysisResult(null);
        setDrawingPaths([]);
        setCanvasKey((prev) => prev + 1); // 强制重新创建画布
        // 重置画板到初始状态
        if (clearCanvas) {
            clearCanvas();
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50">
            <div className="max-w-6xl mx-auto px-4 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    {/* 标题区域 - 只在未完成时显示 */}
                    {!isCompleted && (
                        <Card className="mb-6 shadow-sm">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Title level={2} className="mb-2 text-purple-600">
                                        自由绘画
                                    </Title>
                                    <Text className="text-lg text-gray-600">
                                        发挥您的想象力，创作属于您的艺术作品。没有对错，只有表达。让您的创意自由流淌吧！
                                    </Text>
                                </div>
                                <Button icon={<ArrowLeftOutlined />} onClick={onBack} size="large" className="h-12 px-6">
                                    返回
                                </Button>
                            </div>
                        </Card>
                    )}

                    {/* 完成区域 - 移到画板上方 */}
                    {isCompleted && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5 }}
                            className="mb-6"
                        >
                            <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
                                <div className="text-center">
                                    <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <CheckOutlined className="text-2xl text-white" />
                                    </div>

                                    <Title level={3} className="text-green-600 mb-3">
                                        🎉 创作完成！
                                    </Title>

                                    <Text className="text-lg text-gray-700 block mb-6">
                                        太棒了！您已经完成了一幅美丽的作品。每一笔都是您创意的体现！
                                    </Text>

                                    {/* 分析结果显示 */}
                                    {isAnalyzing && (
                                        <div className="mb-6">
                                            <Spin size="large" />
                                            <Text className="block mt-2 text-gray-600">正在分析您的线条风格...</Text>
                                        </div>
                                    )}

                                    {analysisResult && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border">
                                            <Title level={4} className="text-blue-600 mb-3">
                                                <PictureOutlined className="mr-2" />
                                                您的作品风格分析
                                            </Title>

                                            {/* 线条特征 */}
                                            <div className="w-full">
                                                <div className="grid grid-cols-4 gap-4">
                                                    <div className="text-center p-3 bg-gray-50 rounded">
                                                        <Text className="block text-sm text-gray-600 mb-1">复杂度</Text>
                                                        <Text className="font-medium text-lg">
                                                            {(analysisResult.line_features.complexity * 100).toFixed(1)}
                                                            %
                                                        </Text>
                                                    </div>
                                                    <div className="text-center p-3 bg-gray-50 rounded">
                                                        <Text className="block text-sm text-gray-600 mb-1">节奏感</Text>
                                                        <Text className="font-medium text-lg">
                                                            {(analysisResult.line_features.rhythm * 100).toFixed(1)}%
                                                        </Text>
                                                    </div>
                                                    <div className="text-center p-3 bg-gray-50 rounded">
                                                        <Text className="block text-sm text-gray-600 mb-1">平滑度</Text>
                                                        <Text className="font-medium text-lg">
                                                            {(analysisResult.line_features.smoothness * 100).toFixed(1)}
                                                            %
                                                        </Text>
                                                    </div>
                                                    <div className="text-center p-3 bg-gray-50 rounded">
                                                        <Text className="block text-sm text-gray-600 mb-1">
                                                            曲线特征
                                                        </Text>
                                                        <Text className="font-medium text-lg">
                                                            {analysisResult.line_features.dominant_curves ? "是" : "否"}
                                                        </Text>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* AI生成的艺术作品 */}
                                            {analysisResult.ai_generated_image && (
                                                <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                                                    <Title level={4} className="text-purple-600 mb-3 text-center">
                                                        🎨 AI为您创作的艺术作品
                                                    </Title>

                                                    {analysisResult.ai_generated_image.status === "success" &&
                                                        analysisResult.ai_generated_image.image_url && (
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                {/* 左侧：用户原图 */}
                                                                <div className="text-center">
                                                                    <Text className="block text-lg font-medium text-gray-700 mb-3">
                                                                        📝 您的原创作品
                                                                    </Text>
                                                                    <Image
                                                                        src={savedImage}
                                                                        alt="用户原创作品"
                                                                        className="rounded-lg shadow-lg max-w-full h-auto border-2 border-gray-200"
                                                                        style={{ maxHeight: "300px" }}
                                                                        preview={{
                                                                            mask: (
                                                                                <div className="text-white">
                                                                                    🔍 查看原图
                                                                                </div>
                                                                            ),
                                                                        }}
                                                                    />
                                                                </div>

                                                                {/* 右侧：AI生成图 */}
                                                                <div className="text-center">
                                                                    <Text className="block text-lg font-medium text-purple-600 mb-3">
                                                                        🤖 AI 创作版本
                                                                    </Text>
                                                                    <Image
                                                                        src={analysisResult.ai_generated_image.image_url}
                                                                        alt="AI生成的艺术作品"
                                                                        className="rounded-lg shadow-lg max-w-full h-auto border-2 border-purple-200"
                                                                        style={{ maxHeight: "300px" }}
                                                                        preview={{
                                                                            mask: (
                                                                                <div className="text-white">
                                                                                    🔍 查看AI作品
                                                                                </div>
                                                                            ),
                                                                        }}
                                                                    />

                                                                </div>
                                                            </div>
                                                        )}

                                                    {analysisResult.ai_generated_image.status === "failed" && (
                                                        <div className="text-center text-gray-500">
                                                            <Text>😔 AI创作暂时不可用</Text>
                                                            {analysisResult.ai_generated_image.error && (
                                                                <Text className="text-xs block mt-1">
                                                                    {analysisResult.ai_generated_image.error}
                                                                </Text>
                                                            )}
                                                        </div>
                                                    )}

                                                    {analysisResult.ai_generated_image.status === "generating" && (
                                                        <div className="text-center py-8">
                                                            <div className="relative">
                                                                <Spin size="large" />
                                                                <div className="mt-4">
                                                                    <Text className="block text-lg font-medium text-purple-600">
                                                                        🎨 AI正在为您创作艺术作品
                                                                    </Text>
                                                                    <Text className="block mt-2 text-gray-600">
                                                                        请稍候，这可能需要10-30秒...
                                                                    </Text>
                                                                    <div className="mt-3 flex justify-center items-center space-x-2">
                                                                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                                                                        <div
                                                                            className="w-2 h-2 bg-pink-400 rounded-full animate-bounce"
                                                                            style={{ animationDelay: "0.1s" }}
                                                                        ></div>
                                                                        <div
                                                                            className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                                                                            style={{ animationDelay: "0.2s" }}
                                                                        ></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    <Space size="large">
                                        <Button
                                            type="primary"
                                            size="large"
                                            icon={<CheckOutlined />}
                                            onClick={handleComplete}
                                            className="h-12 px-8 bg-green-500 hover:bg-green-600"
                                        >
                                            完成练习
                                        </Button>

                                        <Button
                                            size="large"
                                            icon={<ReloadOutlined />}
                                            onClick={handleRestart}
                                            className="h-12 px-8"
                                        >
                                            重新创作
                                        </Button>
                                    </Space>
                                </div>
                            </Card>
                        </motion.div>
                    )}

                    {/* 绘画区域 - 只在未完成时显示 */}
                    {!isCompleted && (
                        <div className="transparent-canvas-card">
                            <DrawingCanvas
                                key={canvasKey}
                                width={768}
                                height={1024}
                                onSave={handleSave}
                                hideCanvas={false}
                                onClearRef={setClearCanvas}
                                isCompleted={false}
                                onPathsChange={setDrawingPaths}
                            />
                        </div>
                    )}
                </motion.div>
            </div>
        </div>
    );
};

export default FreeDrawing;
