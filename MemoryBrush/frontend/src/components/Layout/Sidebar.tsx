import React from 'react'
import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  HomeOutlined,
  PlayCircleOutlined,
  PictureOutlined,
  TrophyOutlined,
  StarOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons'

const { Sider } = Layout

interface SidebarProps {
  collapsed?: boolean
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed = false }) => {
  const navigate = useNavigate()
  const location = useLocation()

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/game',
      icon: <PlayCircleOutlined />,
      label: '开始绘画',
    },
    {
      key: '/gallery',
      icon: <PictureOutlined />,
      label: '作品画廊',
    },
    {
      key: '/leaderboard',
      icon: <TrophyOutlined />,
      label: '排行榜',
    },
    {
      key: '/achievements',
      icon: <StarOutlined />,
      label: '成就系统',
    },
    {
      key: '/profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className="fixed left-0 top-16 h-[calc(100vh-64px)] z-40 lg:relative lg:top-0 lg:h-screen shadow-lg"
      width={256}
      collapsedWidth={0}
      breakpoint="lg"
    >
      <motion.div
        initial={{ x: -20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="h-full bg-white"
      >
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0 h-full pt-4"
          style={{ fontSize: '16px' }}
        />
      </motion.div>
    </Sider>
  )
}

export default Sidebar
