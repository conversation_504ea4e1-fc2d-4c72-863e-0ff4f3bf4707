import React, { useState, useEffect } from 'react'
import { Layout, Button, Space, Typography, Avatar, Dropdown } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  HomeOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons'

const { Header: AntHeader } = Layout
const { Text } = Typography

interface HeaderProps {
  collapsed?: boolean
  onToggle?: () => void
}

const Header: React.FC<HeaderProps> = ({ collapsed, onToggle }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 检查全屏状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // 切换全屏
  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (error) {
      console.error('全屏切换失败:', error)
    }
  }

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings')
    },
    {
      key: 'fullscreen',
      icon: isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />,
      label: isFullscreen ? '退出全屏' : '进入全屏',
      onClick: toggleFullscreen
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        // 处理退出登录逻辑
        console.log('退出登录')
      }
    }
  ]

  // 获取页面标题
  const getPageTitle = () => {
    const path = location.pathname
    switch (path) {
      case '/':
        return '首页'
      case '/game':
        return '绘画游戏'
      case '/profile':
        return '个人资料'
      case '/settings':
        return '设置'
      default:
        return '记忆画笔'
    }
  }

  return (
    <AntHeader className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-8 h-20 flex items-center justify-between sticky top-0 z-50">
      {/* 左侧：Logo和标题 */}
      <motion.div
        className="flex items-center gap-2 sm:gap-4"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* 移动端菜单按钮 */}
        <Button
          type="text"
          icon={<MenuOutlined />}
          onClick={onToggle}
          className="lg:hidden"
          size="large"
        />
        
        {/* Logo */}
        <div
          className="flex items-center gap-2 sm:gap-3 cursor-pointer"
          onClick={() => navigate('/')}
        >
          <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-xl font-bold">记</span>
          </div>
          
          <div className="hidden xs:flex flex-col justify-center">
            <div className="text-lg sm:text-xl font-bold text-purple-600 leading-tight">
              记忆画笔
            </div>
            <div className="text-xs sm:text-sm text-gray-500 leading-tight">
              Memory Brush
            </div>
          </div>
        </div>
        
        {/* 页面标题 */}
        <div className="hidden md:block">
          <Text className="text-lg text-gray-400">|</Text>
          <Text className="text-lg text-gray-700 ml-3">{getPageTitle()}</Text>
        </div>
      </motion.div>

      {/* 中间：导航菜单（桌面端） */}
      <motion.div
        className="hidden xl:flex items-center gap-1"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Button
          type={location.pathname === '/' ? 'primary' : 'text'}
          icon={<HomeOutlined />}
          onClick={() => navigate('/')}
          size="middle"
          className="px-3"
        >
          首页
        </Button>

        <Button
          type={location.pathname.startsWith('/game') ? 'primary' : 'text'}
          onClick={() => navigate('/game')}
          size="middle"
          className="px-3"
        >
          开始绘画
        </Button>

        <Button
          type={location.pathname === '/gallery' ? 'primary' : 'text'}
          onClick={() => navigate('/gallery')}
          size="middle"
          className="px-3"
        >
          作品画廊
        </Button>

        <Button
          type={location.pathname === '/leaderboard' ? 'primary' : 'text'}
          onClick={() => navigate('/leaderboard')}
          size="middle"
          className="px-3"
        >
          排行榜
        </Button>
      </motion.div>

      {/* 右侧：用户信息和操作 */}
      <motion.div
        className="flex items-center gap-2 sm:gap-4"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {/* 口号 */}
        <div className="hidden lg:flex flex-col justify-center text-right">
          <Text className="text-purple-500 font-medium leading-tight text-sm lg:text-base">
            画笔在手，奇迹我有！
          </Text>
          <Text className="text-xs text-gray-500 leading-tight">
            My Brush, My Magic!
          </Text>
        </div>

        {/* 全屏按钮 */}
        <Button
          type="text"
          icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
          onClick={toggleFullscreen}
          size="large"
          className="hidden sm:flex"
          title={isFullscreen ? '退出全屏' : '进入全屏'}
        />

        {/* 用户头像和菜单 */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="flex items-center gap-2 sm:gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-1 sm:p-2 transition-colors">
            <Avatar
              size="large"
              icon={<UserOutlined />}
              className="bg-purple-500"
            />
            <div className="hidden md:flex flex-col justify-center">
              <Text className="font-medium leading-tight text-sm md:text-base">
                艺术创作者
              </Text>
              <Text className="text-xs md:text-sm text-gray-500 leading-tight">
                奇迹创造者
              </Text>
            </div>
          </div>
        </Dropdown>
      </motion.div>
    </AntHeader>
  )
}

export default Header
