import React from 'react'
import { Layout, Typography, Space, Divider } from 'antd'
import { HeartOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons'

const { Footer: AntFooter } = Layout
const { Text, Link } = Typography

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()

  return (
    <AntFooter className="border-t border-gray-200 mt-auto" style={{ background: 'rgba(255, 255, 255, 0.9)' }}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          {/* 品牌信息 */}
          <div className="text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl font-bold">记</span>
              </div>
              <div>
                <Text className="text-xl font-bold text-purple-600 block leading-none">
                  记忆画笔
                </Text>
                <Text className="text-sm text-gray-500 leading-none">
                  Memory Brush
                </Text>
              </div>
            </div>
            <Text className="text-gray-600 block mb-2">
              画笔在手，奇迹我有！
            </Text>
            <Text className="text-gray-500">
              My Brush, My Magic!
            </Text>
          </div>

          {/* 联系信息 */}
          <div className="text-center">
            <Text className="text-lg font-semibold text-gray-700 block mb-4">
              联系我们
            </Text>
            <Space direction="vertical" size="small" className="w-full">
              <div className="flex items-center justify-center gap-2">
                <MailOutlined className="text-gray-500" />
                <Link href="mailto:<EMAIL>" className="text-gray-600">
                  <EMAIL>
                </Link>
              </div>
              <div className="flex items-center justify-center gap-2">
                <PhoneOutlined className="text-gray-500" />
                <Text className="text-gray-600">************</Text>
              </div>
            </Space>
          </div>

          {/* 团队信息 */}
          <div className="text-center md:text-right">
            <Text className="text-lg font-semibold text-gray-700 block mb-4">
              奇迹创造者团队
            </Text>
            <Text className="text-gray-600 block mb-2">
              专注于老年人认知健康
            </Text>
            <Text className="text-gray-600 block mb-2">
              用科技传递温暖与关爱
            </Text>
            <div className="flex items-center justify-center md:justify-end gap-1 mt-3">
              <Text className="text-gray-500">用</Text>
              <HeartOutlined className="text-red-400 mx-1" />
              <Text className="text-gray-500">制作</Text>
            </div>
          </div>
        </div>

        <Divider className="my-6" />

        {/* 底部版权信息 */}
        <div className="text-center">
          <Space split={<Divider type="vertical" />} wrap>
            <Text className="text-gray-500">
              © {currentYear} 记忆画笔 Memory Brush
            </Text>
            <Text className="text-gray-500">
              专为老年人群设计的认知艺术疗法游戏
            </Text>
            <Text className="text-gray-500">
              让艺术点亮记忆，让创造温暖心灵
            </Text>
          </Space>
        </div>

        {/* 温馨提示 */}
        <div className="text-center mt-6 p-4 bg-gradient-to-r from-orange-50 to-pink-50 rounded-lg">
          <Text className="text-gray-600">
            💡 温馨提示：建议在光线充足的环境下使用，注意适当休息，保护视力健康
          </Text>
        </div>
      </div>
    </AntFooter>
  )
}

export default Footer
