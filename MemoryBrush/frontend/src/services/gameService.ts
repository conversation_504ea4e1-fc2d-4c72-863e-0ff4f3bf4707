/**
 * 游戏相关的API服务
 */

import apiClient from './api';

// 类型定义
export interface Point {
  x: number;
  y: number;
}

export interface DrawingPath {
  points: Point[];
  color: string;
  size: number;
}

export interface LineAnalysisRequest {
  canvas_data: string;
  paths: DrawingPath[];
}

export interface MatchedArtwork {
  id: string;
  title: string;
  artist: string;
  year: number;
  style: string;
  description: string;
  image_url: string;
}

export interface LineFeatures {
  dominant_curves: boolean;
  complexity: number;
  rhythm: number;
  total_length: number;
  smoothness: number;
  curvature: number;
  direction_changes: number;
}

export interface AIGeneratedImage {
  image_url?: string;
  style?: string;
  prompt?: string;
  status: 'success' | 'failed' | 'generating';
  error?: string;
}

export interface LineAnalysisResult {
  matched_artwork: MatchedArtwork;
  similarity_score: number;
  line_features: LineFeatures;
  suggestions: string[];
  ai_generated_image?: AIGeneratedImage;
}

export interface GameLevel {
  level: number;
  name: string;
  description: string;
  stages: string[];
  difficulty: string;
  estimated_time: number;
}

// 游戏API服务类
class GameService {
  /**
   * 分析用户绘制的线条并匹配名画
   */
  async analyzeLines(request: LineAnalysisRequest): Promise<LineAnalysisResult> {
    try {
      console.log('发送线条分析请求:', {
        canvas_data_length: request.canvas_data.length,
        paths_count: request.paths.length,
        paths_sample: request.paths.slice(0, 2) // 只显示前两个路径作为样本
      });

      const response = await apiClient.post<LineAnalysisResult>('/games/analyze-lines', request);
      console.log('线条分析响应:', response.data);
      return response.data;
    } catch (error) {
      console.error('线条分析失败:', error);
      console.error('请求数据:', request);
      throw new Error('线条分析失败，请稍后重试');
    }
  }

  /**
   * 异步生成AI艺术作品
   */
  async generateAIImage(request: LineAnalysisRequest): Promise<AIGeneratedImage> {
    try {
      console.log('发送AI生图请求');

      const response = await apiClient.post<AIGeneratedImage>('/games/generate-ai-image', request);
      console.log('AI生图响应:', response.data);
      return response.data;
    } catch (error) {
      console.error('AI生图失败:', error);
      return {
        status: 'failed',
        error: 'AI生图失败，请稍后重试'
      };
    }
  }

  /**
   * 获取所有游戏级别
   */
  async getGameLevels(): Promise<GameLevel[]> {
    try {
      const response = await apiClient.get<GameLevel[]>('/games/levels');
      return response.data;
    } catch (error) {
      console.error('获取游戏级别失败:', error);
      throw new Error('获取游戏级别失败');
    }
  }

  /**
   * 获取指定级别的详细信息
   */
  async getGameLevel(levelId: number): Promise<GameLevel> {
    try {
      const response = await apiClient.get<GameLevel>(`/games/levels/${levelId}`);
      return response.data;
    } catch (error) {
      console.error('获取级别详情失败:', error);
      throw new Error('获取级别详情失败');
    }
  }

  /**
   * 获取所有名画列表
   */
  async getFamousArtworks(): Promise<MatchedArtwork[]> {
    try {
      const response = await apiClient.get<MatchedArtwork[]>('/games/artworks');
      return response.data;
    } catch (error) {
      console.error('获取名画列表失败:', error);
      throw new Error('获取名画列表失败');
    }
  }

  /**
   * 获取指定名画的详细信息
   */
  async getArtworkDetails(artworkId: string): Promise<MatchedArtwork> {
    try {
      const response = await apiClient.get(`/games/artworks/${artworkId}`);
      return response.data.data;
    } catch (error) {
      console.error('获取名画详情失败:', error);
      throw new Error('获取名画详情失败');
    }
  }

  /**
   * 开始游戏会话
   */
  async startGameSession(level: number, stage: number = 1, userId?: string) {
    try {
      const response = await apiClient.post('/games/sessions/start', {
        level,
        stage,
        user_id: userId
      });
      return response.data;
    } catch (error) {
      console.error('开始游戏会话失败:', error);
      throw new Error('开始游戏会话失败');
    }
  }

  /**
   * 提交绘画作品
   */
  async submitDrawing(sessionId: string, drawingData: any) {
    try {
      const response = await apiClient.post(`/games/sessions/${sessionId}/submit`, drawingData);
      return response.data;
    } catch (error) {
      console.error('提交作品失败:', error);
      throw new Error('提交作品失败');
    }
  }
}

// 导出服务实例
export const gameService = new GameService();
export default gameService;
