/**
 * 轨迹记录相关API服务
 */

import apiClient from './api';

// 类型定义
export interface Point {
    x: number;
    y: number;
}

export interface TraceStroke {
    id: string;
    points: Point[];
}

export interface TraceSession {
    id: string;
    name: string;
    description?: string;
    strokes: TraceStroke[];
    canvasSize: { width: number; height: number };
    createdAt: string;
    duration: number;
}

export interface TraceSessionCreate {
    name: string;
    description?: string;
    strokes: TraceStroke[];
    canvasSize: { width: number; height: number };
    duration: number;
}

export interface GuidePathPoint {
    x: number;
    y: number;
}

export interface GuidePath {
    id: string;
    points: GuidePathPoint[];
}

export interface GenerateGuideRequest {
    session_id: string;
    simplification_level?: 'low' | 'medium' | 'high';
    min_stroke_length?: number;
    merge_distance?: number;
}

export interface GenerateGuideResponse {
    success: boolean;
    message: string;
    guide_paths?: GuidePath[];
    total_paths?: number;
    canvas_size?: { width: number; height: number };
}

export interface TraceSessionResponse {
    success: boolean;
    message: string;
    data?: TraceSession;
}

/**
 * 轨迹记录API服务类
 */
class TraceService {
    private readonly baseUrl = '/traces';

    /**
     * 创建轨迹会话
     */
    async createSession(sessionData: TraceSessionCreate): Promise<TraceSessionResponse> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/sessions`, sessionData);
            return response.data;
        } catch (error) {
            console.error('创建轨迹会话失败:', error);
            throw error;
        }
    }

    /**
     * 获取轨迹会话列表
     */
    async getSessions(limit: number = 50, offset: number = 0): Promise<TraceSession[]> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/sessions`, {
                params: { limit, offset }
            });
            return response.data;
        } catch (error) {
            console.error('获取轨迹会话列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取指定轨迹会话
     */
    async getSession(sessionId: string): Promise<TraceSession> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/sessions/${sessionId}`);
            return response.data;
        } catch (error) {
            console.error('获取轨迹会话失败:', error);
            throw error;
        }
    }

    /**
     * 删除轨迹会话
     */
    async deleteSession(sessionId: string): Promise<{ success: boolean; message: string }> {
        try {
            const response = await apiClient.delete(`${this.baseUrl}/sessions/${sessionId}`);
            return response.data;
        } catch (error) {
            console.error('删除轨迹会话失败:', error);
            throw error;
        }
    }

    /**
     * 从轨迹会话生成引导线
     */
    async generateGuidePaths(
        sessionId: string,
        options: Omit<GenerateGuideRequest, 'session_id'> = {}
    ): Promise<GenerateGuideResponse> {
        try {
            const request: GenerateGuideRequest = {
                session_id: sessionId,
                simplification_level: options.simplification_level || 'medium',
                min_stroke_length: options.min_stroke_length || 20,
                merge_distance: options.merge_distance || 50
            };

            const response = await apiClient.post(
                `${this.baseUrl}/sessions/${sessionId}/generate-guide`,
                request
            );
            return response.data;
        } catch (error) {
            console.error('生成引导线失败:', error);
            throw error;
        }
    }

    /**
     * 导出轨迹数据为JSON
     */
    exportSessionAsJson(session: TraceSession): void {
        const dataStr = JSON.stringify(session, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `trace_${session.name}_${Date.now()}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    /**
     * 从JSON文件导入轨迹数据
     */
    async importSessionFromJson(file: File): Promise<TraceSession> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (event) => {
                try {
                    const jsonStr = event.target?.result as string;
                    const sessionData = JSON.parse(jsonStr);
                    
                    // 验证数据格式
                    if (!this.validateTraceSession(sessionData)) {
                        throw new Error('无效的轨迹数据格式');
                    }
                    
                    resolve(sessionData as TraceSession);
                } catch (error) {
                    reject(new Error('解析JSON文件失败: ' + (error as Error).message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('读取文件失败'));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * 验证轨迹会话数据格式
     */
    private validateTraceSession(data: any): boolean {
        if (!data || typeof data !== 'object') return false;
        
        const requiredFields = ['id', 'name', 'strokes', 'canvasSize', 'createdAt', 'duration'];
        for (const field of requiredFields) {
            if (!(field in data)) return false;
        }
        
        if (!Array.isArray(data.strokes)) return false;
        if (!data.canvasSize || typeof data.canvasSize !== 'object') return false;
        if (!('width' in data.canvasSize) || !('height' in data.canvasSize)) return false;
        
        return true;
    }

    /**
     * 将轨迹数据保存到本地存储
     */
    saveToLocalStorage(session: TraceSession): void {
        try {
            const savedSessions = this.getFromLocalStorage();
            const existingIndex = savedSessions.findIndex(s => s.id === session.id);
            
            if (existingIndex >= 0) {
                savedSessions[existingIndex] = session;
            } else {
                savedSessions.push(session);
            }
            
            localStorage.setItem('traceSessions', JSON.stringify(savedSessions));
        } catch (error) {
            console.error('保存到本地存储失败:', error);
            throw error;
        }
    }

    /**
     * 从本地存储获取轨迹数据
     */
    getFromLocalStorage(): TraceSession[] {
        try {
            const data = localStorage.getItem('traceSessions');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('从本地存储读取失败:', error);
            return [];
        }
    }

    /**
     * 从本地存储删除轨迹数据
     */
    deleteFromLocalStorage(sessionId: string): void {
        try {
            const savedSessions = this.getFromLocalStorage();
            const filteredSessions = savedSessions.filter(s => s.id !== sessionId);
            localStorage.setItem('traceSessions', JSON.stringify(filteredSessions));
        } catch (error) {
            console.error('从本地存储删除失败:', error);
            throw error;
        }
    }

    /**
     * 清空本地存储的轨迹数据
     */
    clearLocalStorage(): void {
        try {
            localStorage.removeItem('traceSessions');
        } catch (error) {
            console.error('清空本地存储失败:', error);
            throw error;
        }
    }

    /**
     * 计算轨迹统计信息
     */
    calculateSessionStats(session: TraceSession): {
        totalStrokes: number;
        totalPoints: number;
        totalLength: number;
        averageStrokeLength: number;
        duration: number;
    } {
        const totalStrokes = session.strokes.length;
        let totalPoints = 0;
        let totalLength = 0;

        session.strokes.forEach(stroke => {
            totalPoints += stroke.points.length;
            
            // 计算笔画长度
            for (let i = 1; i < stroke.points.length; i++) {
                const dx = stroke.points[i].x - stroke.points[i - 1].x;
                const dy = stroke.points[i].y - stroke.points[i - 1].y;
                totalLength += Math.sqrt(dx * dx + dy * dy);
            }
        });

        return {
            totalStrokes,
            totalPoints,
            totalLength,
            averageStrokeLength: totalStrokes > 0 ? totalLength / totalStrokes : 0,
            duration: session.duration
        };
    }
}

// 创建并导出服务实例
export const traceService = new TraceService();
export default traceService;
