#!/usr/bin/env node

/**
 * 清理轨迹文件，移除多余的字段
 * 移除 color, size, order 字段，只保留 id 和 points
 */

import fs from 'fs';
import path from 'path';

// 要处理的目录列表
const traceDirs = [
    'public/L1-1',
    'public/L1-2', 
    'public/L1-3',
    'public/L1-4',
    'public/L1-5'
];

function cleanTraceFile(filePath) {
    try {
        console.log(`处理文件: ${filePath}`);
        
        // 读取原始文件
        const rawData = fs.readFileSync(filePath, 'utf8');
        const traceData = JSON.parse(rawData);
        
        if (!traceData.strokes || !Array.isArray(traceData.strokes)) {
            console.log(`  跳过: 没有有效的strokes数据`);
            return;
        }
        
        let originalStrokeCount = traceData.strokes.length;
        let cleanedStrokeCount = 0;
        
        // 清理每个stroke，只保留id和points
        const cleanedStrokes = traceData.strokes.map(stroke => {
            if (!stroke.id || !stroke.points || !Array.isArray(stroke.points)) {
                console.log(`  警告: 跳过无效的stroke`);
                return null;
            }
            
            // 计算stroke长度，过滤短线
            let totalLength = 0;
            if (stroke.points.length >= 2) {
                for (let i = 1; i < stroke.points.length; i++) {
                    const dx = stroke.points[i].x - stroke.points[i-1].x;
                    const dy = stroke.points[i].y - stroke.points[i-1].y;
                    totalLength += Math.sqrt(dx * dx + dy * dy);
                }
            }
            
            // 过滤掉短于5像素的线条
            if (totalLength < 5) {
                console.log(`  过滤短线: ${stroke.id}, 长度: ${totalLength.toFixed(2)}px`);
                return null;
            }
            
            cleanedStrokeCount++;
            return {
                id: stroke.id,
                points: stroke.points
            };
        }).filter(stroke => stroke !== null);
        
        // 更新轨迹数据
        const cleanedData = {
            ...traceData,
            strokes: cleanedStrokes
        };
        
        // 写回文件
        fs.writeFileSync(filePath, JSON.stringify(cleanedData, null, 2), 'utf8');
        
        console.log(`  完成: ${originalStrokeCount} -> ${cleanedStrokeCount} strokes`);
        
    } catch (error) {
        console.error(`  错误: ${error.message}`);
    }
}

function main() {
    console.log('开始清理轨迹文件...\n');
    
    for (const dir of traceDirs) {
        const traceFile = path.join(dir, 'trace.json');
        
        if (fs.existsSync(traceFile)) {
            cleanTraceFile(traceFile);
        } else {
            console.log(`跳过: ${traceFile} 不存在`);
        }
    }
    
    console.log('\n清理完成！');
}

// 运行脚本
main();
