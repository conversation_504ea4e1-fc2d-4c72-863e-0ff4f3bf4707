#!/usr/bin/env node

/**
 * 清理轨迹文件，移除backgroundImage字段
 */

import fs from 'fs';
import path from 'path';

// 要处理的目录列表
const traceDirs = [
    'public/L1-1',
    'public/L1-2', 
    'public/L1-3',
    'public/L1-4',
    'public/L1-5'
];

function cleanTraceFile(filePath) {
    try {
        console.log(`处理文件: ${filePath}`);
        
        // 读取原始文件
        const rawData = fs.readFileSync(filePath, 'utf8');
        const traceData = JSON.parse(rawData);
        
        // 检查是否有backgroundImage字段
        if (traceData.backgroundImage !== undefined) {
            console.log(`  移除backgroundImage字段`);
            delete traceData.backgroundImage;
            
            // 写回文件
            fs.writeFileSync(filePath, JSON.stringify(traceData, null, 2), 'utf8');
            console.log(`  完成: backgroundImage字段已移除`);
        } else {
            console.log(`  跳过: 没有backgroundImage字段`);
        }
        
    } catch (error) {
        console.error(`  错误: ${error.message}`);
    }
}

function main() {
    console.log('开始清理轨迹文件中的backgroundImage字段...\n');
    
    for (const dir of traceDirs) {
        const traceFile = path.join(dir, 'trace.json');
        
        if (fs.existsSync(traceFile)) {
            cleanTraceFile(traceFile);
        } else {
            console.log(`跳过: ${traceFile} 不存在`);
        }
    }
    
    console.log('\n清理完成！');
}

// 运行脚本
main();
