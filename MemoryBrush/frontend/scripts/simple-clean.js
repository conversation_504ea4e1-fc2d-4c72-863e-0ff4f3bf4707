import fs from 'fs';

// 直接处理L1-3文件
const filePath = 'public/L1-3/trace.json';

console.log('读取文件...');
const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

console.log(`原始strokes数量: ${data.strokes.length}`);

// 清理strokes
const cleanedStrokes = data.strokes.map(stroke => ({
    id: stroke.id,
    points: stroke.points
}));

console.log(`清理后strokes数量: ${cleanedStrokes.length}`);

// 更新数据
const cleanedData = {
    ...data,
    strokes: cleanedStrokes
};

// 写回文件
fs.writeFileSync(filePath, JSON.stringify(cleanedData, null, 2));

console.log('文件已更新');
