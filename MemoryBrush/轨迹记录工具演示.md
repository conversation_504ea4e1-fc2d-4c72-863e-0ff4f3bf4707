# 轨迹记录工具演示指南

## 快速开始

### 1. 启动应用
```bash
# 启动前端
cd MemoryBrush/frontend
npm run dev

# 启动后端
cd MemoryBrush/backend  
python main.py
```

### 2. 访问工具
- 打开浏览器访问: http://localhost:5173
- 点击右上角的浮动菜单按钮
- 选择"轨迹记录"选项
- 或直接访问: http://localhost:5173/trace-recorder

## 演示步骤

### 第一步：录制轨迹
1. **设置会话信息**
   - 会话名称：输入"演示轨迹1"
   - 会话描述：输入"这是一个演示用的轨迹记录"

2. **配置画笔**
   - 选择喜欢的颜色（如红色 #FF0000）
   - 设置画笔大小为"中 (3px)"

3. **开始录制**
   - 点击"开始录制"按钮
   - 在画布上绘制一些简单的图形：
     - 画一个圆形
     - 画几条直线
     - 画一些曲线
   - 可以尝试暂停和恢复功能
   - 完成后点击"停止"

4. **保存轨迹**
   - 点击"保存轨迹"按钮
   - 系统会自动保存到后端API或本地存储

### 第二步：管理轨迹
1. **切换到轨迹管理**
   - 点击"轨迹管理"标签
   - 查看刚才保存的轨迹会话

2. **查看统计信息**
   - 观察笔画数、点数、录制时长等信息
   - 点击"预览"按钮查看详细信息

3. **导出数据**
   - 点击"导出"按钮下载JSON文件
   - 可以用文本编辑器打开查看数据结构

### 第三步：生成引导线
1. **开始生成**
   - 在轨迹管理中找到刚才的会话
   - 点击"生成引导线"按钮（播放图标）

2. **配置参数**
   - 简化级别：选择"中"
   - 最小笔画长度：保持默认20像素
   - 合并距离：保持默认50像素
   - 点击"生成"按钮

3. **查看结果**
   - 系统会自动切换到"引导线预览"标签
   - 观察生成的引导线效果

### 第四步：预览引导线
1. **播放动画**
   - 点击"播放"按钮观看引导线绘制动画
   - 尝试调整动画速度滑块
   - 使用暂停/恢复功能

2. **切换显示模式**
   - 关闭"显示所有路径"开关
   - 观察单个路径的绘制效果
   - 重新打开查看所有路径

3. **导出引导线**
   - 点击"导出"按钮
   - 下载引导线数据文件

## 高级功能演示

### 1. 复杂图形录制
- 尝试绘制更复杂的图形（如花朵、房子、人物）
- 使用不同的颜色和画笔大小
- 观察系统如何处理复杂轨迹

### 2. 参数调优
- 尝试不同的简化级别：
  - 低：保留更多细节，引导线更精确
  - 高：更简化，引导线更流畅
- 调整最小笔画长度过滤短笔画
- 调整合并距离影响路径连接

### 3. 数据导入导出
- 导出轨迹数据到文件
- 清空本地数据
- 重新导入之前的数据
- 验证数据完整性

## 技术验证

### 1. API功能测试
- 检查浏览器开发者工具的网络标签
- 观察API请求和响应
- 验证数据保存到后端

### 2. 本地存储备份
- 断开网络连接
- 尝试保存轨迹（应该使用本地存储）
- 重新连接网络后验证数据同步

### 3. 性能测试
- 录制长时间的轨迹
- 观察内存使用情况
- 测试大量数据的处理性能

## 预期结果

### 成功指标
1. ✅ 能够成功录制和保存轨迹
2. ✅ 轨迹管理功能正常工作
3. ✅ 引导线生成算法有效
4. ✅ 动画预览流畅播放
5. ✅ 数据导入导出功能完整
6. ✅ API和本地存储都能正常工作

### 质量验证
1. **轨迹精度**: 录制的轨迹应该准确反映绘画过程
2. **引导线质量**: 生成的引导线应该简洁且保持原始形状
3. **用户体验**: 界面响应迅速，操作直观
4. **数据完整性**: 保存和加载的数据应该完整无损

## 故障排除

### 常见问题
1. **画布无响应**: 刷新页面重试
2. **保存失败**: 检查后端服务是否运行
3. **动画卡顿**: 降低动画速度或简化引导线
4. **数据丢失**: 检查本地存储或导出备份

### 调试技巧
1. 打开浏览器开发者工具查看控制台错误
2. 检查网络标签查看API请求状态
3. 查看应用程序标签检查本地存储数据
4. 使用React开发者工具检查组件状态

## 下一步扩展

### 可能的改进
1. **多用户支持**: 添加用户认证和多用户数据隔离
2. **云端同步**: 实现跨设备的数据同步
3. **高级算法**: 改进轨迹处理和引导线生成算法
4. **移动端优化**: 优化触摸设备的使用体验
5. **实时协作**: 支持多人同时录制和分享轨迹

### 集成可能性
1. **与描线练习集成**: 将生成的引导线直接用于TracePractice组件
2. **AI分析**: 使用AI分析绘画风格和技巧
3. **教学系统**: 构建完整的绘画教学平台
4. **数据分析**: 分析用户绘画行为和学习进度
