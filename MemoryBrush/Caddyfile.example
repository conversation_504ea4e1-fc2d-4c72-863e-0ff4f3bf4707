# MemoryBrush Caddy 配置示例

# 生产配置 - 使用域名和自动HTTPS
www.mb.com {
    # 静态文件服务
    root * /var/www/memory-brush/frontend/dist

    # API代理
    reverse_proxy /api/* 127.0.0.1:8000

    # 处理SPA路由
    try_files {path} /index.html

    # 静态资源缓存
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000, immutable"

    # 安全头 (HTTPS增强)
    header {
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "strict-origin-when-cross-origin"
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Robots-Tag "noindex, nofollow"
    }

    # 启用文件服务
    file_server

    # 访问日志
    log {
        output file /var/log/caddy/memory-brush.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }

    # TLS配置 (自动HTTPS)
    tls {
        protocols tls1.2 tls1.3
    }
}

# HTTP重定向到HTTPS
http://www.mb.com {
    redir https://{host}{uri} permanent
}

# 备用IP访问 (重定向到域名)
******* {
    redir https://www.mb.com{uri} permanent
}

# 测试配置 - 使用IP地址 (无HTTPS)
# ******* {
#     # 静态文件服务
#     root * /var/www/memory-brush/frontend/dist
#
#     # API代理
#     reverse_proxy /api/* 127.0.0.1:8000
#
#     # 处理SPA路由
#     try_files {path} /index.html
#
#     # 静态资源缓存
#     @static {
#         path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
#     }
#     header @static Cache-Control "public, max-age=31536000, immutable"
#
#     # 基本安全头
#     header {
#         X-Frame-Options "SAMEORIGIN"
#         X-XSS-Protection "1; mode=block"
#         X-Content-Type-Options "nosniff"
#         Referrer-Policy "strict-origin-when-cross-origin"
#     }
#
#     # 启用文件服务
#     file_server
#
#     # 访问日志
#     log {
#         output file /var/log/caddy/memory-brush.log
#         format json
#     }
# }

# 开发环境配置
# localhost:3000 {
#     reverse_proxy 127.0.0.1:5173
# }

# 健康检查端点
# health.your-domain.com {
#     respond /health 200 {
#         body "OK"
#     }
# }
