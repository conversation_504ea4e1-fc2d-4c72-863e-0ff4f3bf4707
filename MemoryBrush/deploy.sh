#!/bin/bash

# MemoryBrush 部署脚本

set -e

echo "🚀 开始部署 MemoryBrush..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "${YELLOW}📍 检测到服务器IP: ${SERVER_IP}${NC}"

# 提示用户确认配置
echo -e "${YELLOW}⚠️  请确保已经修改以下配置文件中的参数：${NC}"
echo "   - docker-compose.prod.yml 中的 YOUR_SERVER_IP"
echo "   - docker-compose.prod.yml 中的 YOUR_PRODUCTION_SECRET_KEY"
echo "   - frontend/.env.production 中的 API URL"

read -p "是否继续部署？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⏹️  部署已取消${NC}"
    exit 0
fi

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose -f docker-compose.prod.yml down || true

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🧹 清理旧镜像...${NC}"
    docker system prune -f
fi

# 构建并启动容器
echo -e "${YELLOW}🔨 构建并启动容器...${NC}"
docker-compose -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
docker-compose -f docker-compose.prod.yml ps

# 检查服务健康状态
echo -e "${YELLOW}🏥 检查服务健康状态...${NC}"

# 检查前端
if curl -f http://localhost:80 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 前端服务正常${NC}"
else
    echo -e "${RED}❌ 前端服务异常${NC}"
fi

# 检查后端
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务正常${NC}"
else
    echo -e "${RED}❌ 后端服务异常${NC}"
fi

echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${GREEN}📱 前端访问地址: http://${SERVER_IP}${NC}"
echo -e "${GREEN}🔧 后端API地址: http://${SERVER_IP}:8000${NC}"
echo ""
echo -e "${YELLOW}📋 常用命令：${NC}"
echo "   查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "   重启服务: docker-compose -f docker-compose.prod.yml restart"
echo "   停止服务: docker-compose -f docker-compose.prod.yml down"
echo "   更新服务: ./deploy.sh"
