# 🌐 MemoryBrush 域名配置指南

## 📋 配置信息
- **服务器IP**: `*******`
- **域名**: `www.mb.com`
- **自动HTTPS**: ✅ 启用
- **SSL证书**: Let's Encrypt (自动获取和续期)

## 🔧 DNS 配置

### 1. 添加 A 记录
在您的域名服务商管理面板中添加以下DNS记录：

```
类型: A
主机: www
值: *******
TTL: 300 (或默认)
```

### 2. 可选：添加根域名重定向
```
类型: A
主机: @
值: *******
TTL: 300
```

### 3. 验证DNS解析
```bash
# 检查DNS解析
nslookup www.mb.com
dig www.mb.com

# 应该返回IP地址 *******
```

## 🚀 部署步骤

### 1. 确保DNS解析正确
```bash
# 在服务器上验证
ping www.mb.com
# 应该显示 *******
```

### 2. 运行部署脚本
```bash
cd MemoryBrush
./deploy-direct.sh
```

### 3. 等待SSL证书获取
- Caddy会自动向Let's Encrypt申请SSL证书
- 首次获取可能需要1-2分钟
- 证书会自动续期

## 🔒 HTTPS 配置详情

### 自动完成的配置
- ✅ **SSL证书获取**: Let's Encrypt免费证书
- ✅ **自动续期**: 证书到期前自动更新
- ✅ **HTTP重定向**: 自动重定向到HTTPS
- ✅ **安全头**: HSTS, CSP等安全配置
- ✅ **现代TLS**: 仅支持TLS 1.2+

### 访问地址
- **主要地址**: https://www.mb.com
- **HTTP访问**: http://www.mb.com (自动重定向到HTTPS)
- **IP访问**: http://******* (重定向到域名HTTPS)

## 🔍 验证部署

### 1. 检查SSL证书
```bash
# 检查证书信息
openssl s_client -connect www.mb.com:443 -servername www.mb.com

# 或使用在线工具
# https://www.ssllabs.com/ssltest/
```

### 2. 测试访问
```bash
# 测试HTTPS访问
curl -I https://www.mb.com

# 测试HTTP重定向
curl -I http://www.mb.com

# 应该返回 301/302 重定向到HTTPS
```

### 3. 检查服务状态
```bash
# 查看Caddy状态
sudo systemctl status caddy

# 查看证书获取日志
sudo journalctl -u caddy -f | grep -i "certificate"
```

## 🛠️ 故障排除

### 1. DNS解析问题
```bash
# 检查DNS传播
dig www.mb.com @*******
dig www.mb.com @*******

# 如果解析不正确，等待DNS传播（最多24小时）
```

### 2. SSL证书获取失败
```bash
# 查看详细错误
sudo journalctl -u caddy -n 50 | grep -i "error\|fail"

# 常见原因：
# - DNS未正确解析
# - 防火墙阻止80/443端口
# - 域名已有其他证书
```

### 3. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --list-all
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 4. 手动重新获取证书
```bash
# 停止Caddy
sudo systemctl stop caddy

# 删除旧证书（如果存在）
sudo rm -rf /var/lib/caddy/.local/share/caddy/certificates/

# 重启Caddy
sudo systemctl start caddy

# 查看日志
sudo journalctl -u caddy -f
```

## 📊 监控和维护

### 1. 证书监控
```bash
# 检查证书到期时间
echo | openssl s_client -connect www.mb.com:443 2>/dev/null | openssl x509 -noout -dates

# 设置证书到期提醒（可选）
# 添加到crontab
0 0 * * 0 /usr/bin/openssl s_client -connect www.mb.com:443 2>/dev/null | openssl x509 -noout -checkend 604800 || echo "SSL certificate expires soon" | mail -s "SSL Alert" <EMAIL>
```

### 2. 性能监控
```bash
# 检查HTTPS性能
curl -w "@curl-format.txt" -o /dev/null -s https://www.mb.com

# curl-format.txt 内容：
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

### 3. 日志分析
```bash
# 查看访问日志
sudo tail -f /var/log/caddy/memory-brush.log | jq

# 分析SSL握手
sudo journalctl -u caddy | grep -i "tls\|ssl\|certificate"
```

## 🎯 最佳实践

### 1. 安全配置
- ✅ 使用强密码和密钥
- ✅ 定期更新系统和软件
- ✅ 监控访问日志
- ✅ 设置防火墙规则

### 2. 性能优化
- ✅ 启用HTTP/2 (Caddy默认)
- ✅ 配置静态资源缓存
- ✅ 使用CDN (可选)
- ✅ 压缩静态文件

### 3. 备份策略
```bash
# 备份Caddy配置
sudo cp /etc/caddy/Caddyfile /backup/Caddyfile.$(date +%Y%m%d)

# 备份SSL证书
sudo tar -czf /backup/caddy-certs-$(date +%Y%m%d).tar.gz /var/lib/caddy/

# 备份应用数据
sudo tar -czf /backup/memory-brush-$(date +%Y%m%d).tar.gz /var/www/memory-brush/
```

## 📞 技术支持

### 常用命令速查
```bash
# 重启服务
sudo systemctl restart caddy

# 重载配置
sudo caddy reload

# 查看状态
sudo systemctl status caddy

# 查看日志
sudo journalctl -u caddy -f

# 验证配置
sudo caddy validate --config /etc/caddy/Caddyfile

# 查看证书信息
sudo caddy list-certificates
```

### 联系信息
如果遇到问题，请检查：
1. DNS解析是否正确
2. 防火墙端口是否开放
3. 服务器时间是否正确
4. 域名是否已备案（如需要）

🎉 配置完成后，您的MemoryBrush应用将通过 https://www.mb.com 安全访问！
