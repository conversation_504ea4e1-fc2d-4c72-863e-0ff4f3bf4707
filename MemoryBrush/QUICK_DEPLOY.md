# ⚡ MemoryBrush 快速部署指南

## 🎯 5分钟快速部署

### 1. 准备服务器
- Ubuntu 20.04+ 或 CentOS 7+
- 2GB+ 内存，10GB+ 存储
- 开放端口 80 和 8000

### 2. 一键安装 Docker
```bash
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER
```

### 3. 安装 Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 4. 上传项目并配置
```bash
# 上传 MemoryBrush 文件夹到服务器
cd MemoryBrush

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "服务器IP: $SERVER_IP"

# 修改配置文件
sed -i "s/YOUR_SERVER_IP/$SERVER_IP/g" docker-compose.prod.yml
sed -i "s/YOUR_SERVER_IP/$SERVER_IP/g" frontend/.env.production
sed -i "s/YOUR_PRODUCTION_SECRET_KEY/$(openssl rand -hex 32)/g" docker-compose.prod.yml
```

### 5. 一键部署
```bash
./deploy.sh
```

### 6. 访问应用
- 前端: http://YOUR_SERVER_IP
- 后端: http://YOUR_SERVER_IP:8000

## 🔧 常用命令

```bash
# 查看状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

## 🆘 遇到问题？

1. **检查Docker状态**: `sudo systemctl status docker`
2. **查看详细日志**: `docker-compose -f docker-compose.prod.yml logs`
3. **重新登录**: 安装Docker后需要重新登录
4. **检查端口**: `sudo netstat -tlnp | grep :80`

详细部署指南请查看 `DEPLOYMENT.md`
