# 记忆画笔开发指南

## 快速开始

### 环境准备

1. **Node.js 18+** - 前端开发环境
2. **Python 3.9+** - 后端开发环境
3. **Git** - 版本控制

### 本地开发

#### 1. 克隆项目
```bash
git clone <repository-url>
cd Demo/MemoryBrush
```

#### 2. 环境配置
```bash
# 复制环境变量配置
cp .env.example .env
# 根据需要修改 .env 文件中的配置
```

#### 3. 后端启动
```bash
cd backend
pip install -r requirements.txt
python main.py
```
后端服务将在 http://localhost:8000 启动

#### 4. 前端启动
```bash
cd frontend
npm install
npm run dev
```
前端服务将在 http://localhost:5173 启动

### Docker开发

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 项目结构

```
Demo/MemoryBrush/
├── frontend/                 # React前端
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── stores/         # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── types/          # TypeScript类型
│   │   └── styles/         # 样式文件
│   └── public/             # 静态资源
├── backend/                 # FastAPI后端
│   ├── app/
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   └── main.py            # 应用入口
└── docs/                   # 项目文档
```

## 技术栈

### 前端
- **React 18** - UI框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Ant Design** - UI组件库
- **Tailwind CSS** - 样式框架
- **Fabric.js** - Canvas绘图库
- **Framer Motion** - 动画库
- **Zustand** - 状态管理

### 后端
- **FastAPI** - Web框架
- **SQLAlchemy** - ORM
- **SQLite/PostgreSQL** - 数据库
- **OpenCV** - 图像处理
- **TensorFlow** - AI模型
- **Loguru** - 日志管理

## 开发规范

### 代码风格

#### 前端
- 使用ESLint + Prettier进行代码格式化
- 组件使用PascalCase命名
- 文件名使用PascalCase（组件）或camelCase（工具）
- 使用TypeScript严格模式

#### 后端
- 使用Black + isort进行代码格式化
- 函数和变量使用snake_case命名
- 类使用PascalCase命名
- 遵循PEP 8规范

### Git工作流

1. **分支命名**
   - `feature/功能名称` - 新功能开发
   - `bugfix/问题描述` - Bug修复
   - `hotfix/紧急修复` - 紧急修复

2. **提交信息**
   ```
   类型(范围): 简短描述
   
   详细描述（可选）
   ```
   
   类型：feat, fix, docs, style, refactor, test, chore

### API设计

- 使用RESTful API设计原则
- 统一的响应格式：
  ```json
  {
    "success": true,
    "message": "操作成功",
    "data": {...}
  }
  ```
- 使用HTTP状态码表示请求结果
- API文档自动生成（FastAPI Swagger）

## 测试

### 前端测试
```bash
cd frontend
npm run test
```

### 后端测试
```bash
cd backend
pytest
```

## 部署

### 生产环境构建

#### 前端
```bash
cd frontend
npm run build
```

#### 后端
```bash
cd backend
# 设置生产环境变量
export DEBUG=False
export DATABASE_URL=postgresql://...
python main.py
```

### Docker部署
```bash
# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 常见问题

### 1. 端口冲突
如果端口被占用，可以修改配置：
- 前端：修改 `vite.config.ts` 中的端口
- 后端：修改 `.env` 中的 `PORT` 变量

### 2. 依赖安装失败
```bash
# 清除缓存
npm cache clean --force
pip cache purge

# 重新安装
npm install
pip install -r requirements.txt
```

### 3. 数据库连接问题
检查 `.env` 文件中的 `DATABASE_URL` 配置

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 发起Pull Request

## 联系方式

- 项目负责人：奇迹创造者团队
- 邮箱：<EMAIL>
- 文档：查看 `docs/` 目录

---

**记忆画笔 - 画笔在手，奇迹我有！**

# AI生图功能配置指南

## 🎨 功能说明

记忆画笔集成了SiliconFlow的AI生图功能，可以基于用户的线条绘画生成完整的艺术作品。

## 🔧 配置步骤

### 1. 获取SiliconFlow API密钥

1. 访问 [SiliconFlow官网](https://siliconflow.cn/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制您的API密钥

### 2. 配置环境变量

编辑 `MemoryBrush/backend/.env` 文件：

```bash
# 将下面的测试密钥替换为您的真实API密钥
SILICONFLOW_API_KEY=your_real_api_key_here

# 其他AI配置（可选）
AI_IMAGE_MODEL=Kwai-Kolors/Kolors
AI_IMAGE_SIZE=1024x1024
AI_IMAGE_STEPS=20
AI_IMAGE_GUIDANCE_SCALE=7.5
```

### 3. 重启后端服务

```bash
cd MemoryBrush/backend
python main.py
```

## 🎯 支持的模型

根据SiliconFlow文档，支持以下模型：

- `Kwai-Kolors/Kolors` (推荐)
- `stabilityai/stable-diffusion-xl-base-1.0`
- 其他兼容模型

## 🔍 API调用示例

系统会自动调用以下格式的API：

```bash
curl --request POST \
  --url https://api.siliconflow.cn/v1/images/generations \
  --header 'Authorization: Bearer YOUR_API_KEY' \
  --header 'Content-Type: application/json' \
  --data '{
    "model": "Kwai-Kolors/Kolors",
    "prompt": "A beautiful artwork in the style of impressionism, incorporating the user'\''s hand-drawn lines",
    "image_size": "1024x1024",
    "batch_size": 1,
    "num_inference_steps": 20,
    "guidance_scale": 7.5,
    "image": "data:image/png;base64,..."
  }'
```

## 🚀 功能特性

- **智能风格匹配**: 基于用户线条特征匹配艺术风格
- **线条保留**: AI生成时保留用户的原始线条结构
- **多种风格**: 支持印象派、抽象派、现实主义等多种艺术风格
- **高质量输出**: 生成1024x1024高分辨率图像

## 🛠️ 故障排除

### 问题1: API密钥错误
- 确认API密钥正确无误
- 检查API密钥是否有足够的配额

### 问题2: 网络连接问题
- 确认服务器可以访问 `api.siliconflow.cn`
- 检查防火墙设置

### 问题3: 模型不支持
- 使用推荐的模型 `Kwai-Kolors/Kolors`
- 查看SiliconFlow文档获取最新支持的模型列表

## 📝 演示模式

如果没有配置真实API密钥，系统会运行在演示模式下：
- 显示占位图像
- 所有其他功能正常工作
- 用于开发和测试

## 📞 技术支持

如有问题，请参考：
- [SiliconFlow官方文档](https://docs.siliconflow.cn/)
- [API参考文档](https://docs.siliconflow.cn/cn/api-reference/images/images-generations)
