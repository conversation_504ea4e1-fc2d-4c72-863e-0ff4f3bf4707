# 记忆画笔 (Memory Brush)
## 认知艺术疗法游戏

**主题**: 记忆画笔 (Memory Brush)
**口号**: 画笔在手，奇迹我有！/ My Brush, My Magic!
**团队**: 奇迹创造者 (Magic Makers)

---

## 项目概述

针对老年人群，特别是阿尔茨海默症和帕金森疾病患者的 Web 绘画游戏。通过分层次的绘画活动，促进认知功能维护和艺术表达。

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript
- **绘图**: HTML5 Canvas + Fabric.js
- **UI 组件**: Ant Design + 自定义组件
- **动画**: Framer Motion
- **状态管理**: Zustand
- **构建工具**: Vite
- **样式**: Tailwind CSS + CSS Modules

### 后端技术栈
- **框架**: FastAPI (Python)
- **图像处理**: OpenCV + PIL
- **AI 模型**: TensorFlow/PyTorch （图像识别）
- **数据库**: SQLite （开发） / PostgreSQL （生产）
- **文件存储**: 本地存储 + 云存储支持

## 游戏级别设计

### Level 1: 简单线条
- **1.1 线段绘制**: 自由画线生成艺术作品
- **1.2 描摹练习**: 显示部分图像，用户描摹完成
- **1.3 几何图形**: 绘制规则 / 不规则的直线和曲线图形

### Level 2: 二维转三维
- **2.1 基础立体**: 锥形、立方体、圆柱体绘制
- **2.2 色彩填充**: 根据色系和色谱进行填色
- **2.3 质感绘制**: 复杂曲线描边，选择画笔质感

### Level 3: 引导创作
- **3.1 抽象艺术**: 抽象线条和色块组合
- **3.2 几何静物**: 几何形状静物绘制
- **3.3 生活物品**: 日常用品的艺术化表现

### Level 4: 照片艺术化
- **4.1 轮廓提取**: 上传照片，提取抽象轮廓
- **4.2 风格选择**: 多种艺术风格可选
- **4.3 智能渲染**: AI 辅助生成最终艺术作品

## 项目结构

```
Demo/MemoryBrush/
├── frontend/               # 前端项目
│   ├── public/             # 静态资源
│   ├── src/                # 源代码
│   │   ├── components/     # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── utils/          # 工具函数
│   │   ├── stores/         # 状态管理
│   │   ├── types/          # TypeScript类型定义
│   │   └── styles/         # 样式文件
│   ├── package.json
│   └── vite.config.ts
├── backend/               # 后端项目
│   ├── app/               # 应用代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── requirements.txt
│   └── main.py
├── docs/                   # 项目文档
├── assets/                 # 共享资源
└── README.md
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- npm/yarn/pnpm

### 安装依赖

#### 前端
```bash
cd frontend
npm install
```

#### 后端
```bash
cd backend
pip install -r requirements.txt
```

### 开发运行

#### 启动后端
```bash
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 启动前端
```bash
cd frontend
npm run dev
```

访问：http://localhost:5173

## 核心功能特性

### 🎨 绘画功能
- 支持鼠标和触摸操作
- 多种画笔工具和效果
- 实时画布预览
- 撤销 / 重做功能

### 🧠 认知训练
- 分级难度设计
- 进度跟踪记录
- 个性化推荐
- 成就系统

### 🎯 用户体验
- 大字体、高对比度界面
- 简化操作流程
- 语音提示支持
- 无障碍设计

### 📊 数据分析
- 绘画行为分析
- 认知能力评估
- 进步趋势报告
- 医疗数据导出

## 开发规范

### 代码规范
- ESLint + Prettier （前端）
- Black + isort （后端）
- 统一的命名规范
- 详细的注释文档

### Git 工作流
- main: 主分支
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 修复分支

## 部署说明

### Docker 部署
```bash
docker-compose up -d
```

### 传统部署
详见 `docs/deployment.md`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

---

**记忆画笔 - 让艺术点亮记忆，让创造温暖心灵**

线条启蒙 Level 1
L1-1 自由画线
L1-2 描线练习
L1-3 图像描线
L1-4 直线图形
L1-5 曲线图形

立体空间 Level 2
画面构图 Level 3
智能创作 Level 4
