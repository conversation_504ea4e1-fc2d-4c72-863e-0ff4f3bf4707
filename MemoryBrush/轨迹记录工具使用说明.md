# 轨迹记录工具使用说明

## 概述

轨迹记录工具是一个独立的小工具，用于记录用户的绘画轨迹并将其转换为引导线，可用于描线练习或其他绘画辅助功能。

## 功能特性

### 1. 轨迹记录 📝
- **实时绘画记录**: 记录用户的每一笔画，包括坐标、时间戳、颜色、粗细等信息
- **录制控制**: 支持开始、暂停、恢复、停止录制
- **画笔设置**: 可调整画笔颜色和粗细
- **会话管理**: 为每次录制设置名称和描述

### 2. 轨迹管理 📁
- **会话列表**: 查看所有已保存的轨迹会话
- **统计信息**: 显示笔画数、点数、录制时长等统计数据
- **数据操作**: 支持预览、导出、删除轨迹会话
- **导入导出**: 支持JSON格式的数据导入导出

### 3. 引导线生成 🎯
- **智能转换**: 将轨迹数据转换为引导线格式
- **参数配置**: 可调整简化级别、最小笔画长度、合并距离等参数
- **动画预览**: 支持动画播放引导线绘制过程
- **多种显示模式**: 支持显示所有路径或单个路径

## 使用方法

### 访问工具
1. 在应用中点击浮动导航菜单
2. 选择"轨迹记录"选项
3. 或直接访问 `/trace-recorder` 路径

### 录制轨迹
1. **设置会话信息**:
   - 输入会话名称（必填）
   - 输入会话描述（可选）

2. **配置画笔**:
   - 选择画笔颜色
   - 设置画笔粗细（1-8像素）

3. **开始录制**:
   - 点击"开始录制"按钮
   - 在画布上自由绘画
   - 可以随时暂停/恢复录制
   - 点击"停止"结束录制

4. **保存数据**:
   - 录制完成后点击"保存轨迹"
   - 数据会自动保存到服务器（如果可用）或本地存储

### 管理轨迹
1. **查看会话列表**:
   - 切换到"轨迹管理"标签
   - 查看所有已保存的会话及其统计信息

2. **操作会话**:
   - 👁️ **预览**: 查看会话详细信息
   - ▶️ **生成引导线**: 将轨迹转换为引导线
   - 📥 **导出**: 下载会话数据为JSON文件
   - 🗑️ **删除**: 删除不需要的会话

3. **导入会话**:
   - 点击"导入会话"按钮
   - 选择JSON格式的轨迹文件
   - 数据会自动导入到系统中

### 生成引导线
1. **选择会话**:
   - 在轨迹管理中找到要转换的会话
   - 点击"生成引导线"按钮

2. **配置参数**:
   - **简化级别**: 
     - 低：保留更多细节
     - 中：平衡处理
     - 高：更简化的线条
   - **最小笔画长度**: 过滤掉太短的笔画（像素）
   - **合并距离**: 相近笔画的合并距离（像素）

3. **生成和预览**:
   - 点击"生成"按钮
   - 自动切换到"引导线预览"标签
   - 查看生成的引导线效果

### 预览引导线
1. **播放动画**:
   - 点击"播放"按钮观看引导线绘制动画
   - 可以暂停/恢复动画
   - 点击"重置"回到开始状态

2. **调整设置**:
   - **动画速度**: 调整播放速度（0.1-3倍速）
   - **显示模式**: 选择显示所有路径或单个路径

3. **导出引导线**:
   - 点击"导出"按钮
   - 下载引导线数据为JSON格式

## 技术特性

### 数据格式
- **轨迹数据**: 包含完整的绘画过程信息
- **引导线数据**: 优化后的路径信息，适用于描线练习
- **JSON格式**: 便于数据交换和备份

### 数据处理
- **路径简化**: 减少冗余点，保持线条流畅
- **路径合并**: 将相近的笔画合并为连续路径
- **平滑处理**: 对线条进行平滑化处理
- **智能过滤**: 过滤掉过短或无效的笔画

### 存储方式
- **服务器存储**: 优先保存到后端API
- **本地存储**: API不可用时使用浏览器本地存储
- **文件导出**: 支持JSON格式的文件导出

## 应用场景

### 1. 描线练习
- 记录标准绘画轨迹
- 生成练习用的引导线
- 为学习者提供绘画指导

### 2. 绘画教学
- 教师演示绘画过程
- 学生跟随引导线练习
- 记录和分析绘画技巧

### 3. 艺术创作
- 记录创作过程
- 分析绘画习惯
- 重现绘画步骤

### 4. 数据分析
- 分析绘画行为
- 研究绘画模式
- 优化绘画工具

## 注意事项

1. **浏览器兼容性**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
2. **数据备份**: 重要数据请及时导出备份
3. **性能考虑**: 长时间录制可能产生大量数据，注意性能影响
4. **网络连接**: API功能需要网络连接，离线时使用本地存储

## 故障排除

### 常见问题
1. **无法保存数据**: 检查网络连接，或使用导出功能备份数据
2. **画布无响应**: 刷新页面重试
3. **动画卡顿**: 降低动画速度或简化引导线
4. **导入失败**: 检查JSON文件格式是否正确

### 技术支持
如遇到技术问题，请检查浏览器控制台的错误信息，或联系开发团队获取支持。
