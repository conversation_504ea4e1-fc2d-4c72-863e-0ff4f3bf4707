# ⚡ MemoryBrush 直接部署 - 3分钟快速指南

## 🎯 最简部署步骤

### 1. 准备服务器
- Ubuntu 20.04+ 或 Debian 10+
- 1GB+ 内存，5GB+ 存储
- 开放端口 80 和 8000

### 2. 上传项目文件
```bash
# 方法1: 使用scp上传
scp -r MemoryBrush/ user@your-server:/home/<USER>/

# 方法2: 使用git克隆
git clone your-repository-url
cd MemoryBrush
```

### 3. 一键部署
```bash
cd MemoryBrush
./deploy-direct.sh
```

### 4. 访问应用
- 前端: http://YOUR_SERVER_IP
- 后端: http://YOUR_SERVER_IP:8000

## 🔧 脚本会自动完成

✅ 安装 Node.js 18+  
✅ 安装 Python 3 + pip  
✅ 安装 Nginx  
✅ 安装系统依赖  
✅ 创建Python虚拟环境  
✅ 安装后端依赖  
✅ 配置后端服务  
✅ 构建前端应用  
✅ 配置Nginx  
✅ 设置防火墙  
✅ 启动所有服务  

## 🛠️ 常用命令

```bash
# 查看服务状态
sudo systemctl status memory-brush-backend nginx

# 查看日志
sudo journalctl -u memory-brush-backend -f
sudo tail -f /var/log/nginx/access.log

# 重启服务
sudo systemctl restart memory-brush-backend
sudo systemctl restart nginx

# 更新应用
git pull
./deploy-direct.sh
```

## 🆘 遇到问题？

1. **权限问题**: 确保不使用root用户运行脚本
2. **端口占用**: `sudo netstat -tlnp | grep :80`
3. **服务异常**: `sudo journalctl -u memory-brush-backend -n 20`
4. **网络问题**: 检查防火墙设置

详细故障排除请查看 `DIRECT_DEPLOY.md`

## 💡 优势

- 🚀 **快速**: 3分钟完成部署
- 💾 **省资源**: 比Docker节省50%内存
- 🔧 **易维护**: 标准Linux服务管理
- 📊 **高性能**: 直接运行，无容器开销
