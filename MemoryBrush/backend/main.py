"""
记忆画笔 (Memory Brush) - 后端主入口
认知艺术疗法游戏后端服务

主题: 记忆画笔 (Memory Brush)
口号: 画笔在手，奇迹我有！/ My Brush, My Magic!
团队: 奇迹创造者 (Magic Makers)
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

# 导入应用模块
from app.core.config import settings
from app.core.database import init_db
from app.api.routes import api_router
from app.core.logging import setup_logging

# 设置日志
logger = setup_logging()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🎨 记忆画笔服务启动中...")
    logger.info("🖌️ 画笔在手，奇迹我有！")
    
    # 初始化数据库
    await init_db()
    logger.info("📊 数据库初始化完成")
    
    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("static/images", exist_ok=True)
    os.makedirs("static/artworks", exist_ok=True)
    logger.info("📁 文件目录创建完成")
    
    yield
    
    # 关闭时执行
    logger.info("🎨 记忆画笔服务关闭")


# 创建FastAPI应用
app = FastAPI(
    title="记忆画笔 API",
    description="""
    ## 记忆画笔 (Memory Brush) - 认知艺术疗法游戏

    **主题**: 记忆画笔 (Memory Brush)  
    **口号**: 画笔在手，奇迹我有！/ My Brush, My Magic!  
    **团队**: 奇迹创造者 (Magic Makers)

    ### 功能特性
    - 🎨 多级别绘画游戏
    - 🧠 认知功能评估
    - 📊 进度跟踪分析
    - 🎯 个性化推荐
    - 🏆 成就系统

    ### 目标用户
    专为老年人群，特别是阿尔茨海默症和帕金森疾病患者设计
    """,
    version="1.0.0",
    contact={
        "name": "奇迹创造者团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan,
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# API路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """根路径 - 服务状态检查"""
    return {
        "message": "记忆画笔 API 服务运行中",
        "slogan": "画笔在手，奇迹我有！",
        "english_slogan": "My Brush, My Magic!",
        "team": "奇迹创造者 (Magic Makers)",
        "version": "1.0.0",
        "status": "healthy",
        "features": [
            "多级别绘画游戏",
            "认知功能评估", 
            "进度跟踪分析",
            "个性化推荐",
            "成就系统"
        ]
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "Memory Brush API",
        "timestamp": "2024-01-01T00:00:00Z"
    }


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "status_code": 500
        }
    )


if __name__ == "__main__":
    # 开发环境运行
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning",
        access_log=settings.DEBUG,
    )
