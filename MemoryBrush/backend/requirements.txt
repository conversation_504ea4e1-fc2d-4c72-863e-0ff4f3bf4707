# 核心Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart

# 数据库
sqlalchemy>=2.0.0
python-dotenv

# 基础工具
requests
aiofiles
aiohttp  # 异步HTTP客户端，用于AI API调用

# 日志
loguru

# 图像处理和数值计算（线条分析功能需要）
opencv-python>=4.8.0  # 图像处理
Pillow>=10.0.0         # 图像处理
numpy>=1.24.0          # 数值计算

# 可选依赖（如果需要完整功能，可以逐个安装）
# pandas         # 数据处理
# matplotlib     # 图表绘制
# tensorflow     # AI/ML
# torch          # AI/ML
# scikit-learn   # 机器学习
# pytest         # 测试
# black          # 代码格式化
# isort          # 导入排序
# pydantic-settings  # 设置管理
