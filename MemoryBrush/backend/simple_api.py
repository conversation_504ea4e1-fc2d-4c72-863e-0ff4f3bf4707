"""
简化的API服务器，用于测试线条分析功能
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import math

# 创建FastAPI应用
app = FastAPI(
    title="MemoryBrush API",
    description="记忆画笔 - 认知训练绘画应用API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class Point(BaseModel):
    x: float
    y: float

class DrawingPath(BaseModel):
    points: List[Point]
    color: str
    size: int

class LineAnalysisRequest(BaseModel):
    canvas_data: str
    paths: List[DrawingPath]

class MatchedArtwork(BaseModel):
    id: str
    title: str
    artist: str
    year: int
    style: str
    description: str
    image_url: str

class LineFeatures(BaseModel):
    dominant_curves: bool
    complexity: float
    rhythm: float
    total_length: float
    smoothness: float
    curvature: float
    direction_changes: int

class LineAnalysisResult(BaseModel):
    matched_artwork: MatchedArtwork
    similarity_score: float
    line_features: LineFeatures
    suggestions: List[str]

# 世界名画数据库
FAMOUS_ARTWORKS = [
    {
        "id": "starry_night",
        "title": "星夜",
        "artist": "文森特·梵高",
        "year": 1889,
        "style": "后印象派",
        "description": "梵高最著名的作品之一，以其独特的旋涡状笔触和鲜艳的色彩而闻名。",
        "image_url": "/static/artworks/starry_night.jpg",
        "line_features": {
            "dominant_curves": True,
            "spiral_patterns": True,
            "flowing_lines": True,
            "complexity": 0.8,
            "rhythm": 0.9
        }
    },
    {
        "id": "great_wave",
        "title": "神奈川冲浪里",
        "artist": "葛饰北斋",
        "year": 1831,
        "style": "浮世绘",
        "description": "日本最著名的浮世绘作品，以其动态的波浪线条而闻名。",
        "image_url": "/static/artworks/great_wave.jpg",
        "line_features": {
            "dominant_curves": True,
            "wave_patterns": True,
            "flowing_lines": True,
            "complexity": 0.7,
            "rhythm": 0.8
        }
    },
    {
        "id": "mona_lisa",
        "title": "蒙娜丽莎",
        "artist": "列奥纳多·达·芬奇",
        "year": 1503,
        "style": "文艺复兴",
        "description": "世界上最著名的肖像画，以其神秘的微笑和精细的线条而闻名。",
        "image_url": "/static/artworks/mona_lisa.jpg",
        "line_features": {
            "dominant_curves": False,
            "smooth_lines": True,
            "detailed_lines": True,
            "complexity": 0.6,
            "rhythm": 0.5
        }
    }
]

def analyze_line_features(paths: List[DrawingPath]) -> Dict[str, Any]:
    """分析用户绘制的线条特征"""
    if not paths:
        return {
            "dominant_curves": False,
            "complexity": 0.0,
            "rhythm": 0.0,
            "total_length": 0.0,
            "smoothness": 0.0,
            "curvature": 0.0,
            "direction_changes": 0
        }
    
    total_length = 0.0
    total_curvature = 0.0
    direction_changes = 0
    total_points = 0
    
    for path in paths:
        points = path.points
        if len(points) < 2:
            continue
            
        # 计算路径长度和曲率
        path_length = 0.0
        path_curvature = 0.0
        prev_direction = None
        
        for i in range(1, len(points)):
            # 计算距离
            dx = points[i].x - points[i-1].x
            dy = points[i].y - points[i-1].y
            distance = math.sqrt(dx*dx + dy*dy)
            path_length += distance
            
            # 计算方向变化
            if distance > 0:
                direction = math.atan2(dy, dx)
                if prev_direction is not None:
                    direction_change = abs(direction - prev_direction)
                    if direction_change > math.pi:
                        direction_change = 2 * math.pi - direction_change
                    path_curvature += direction_change
                    if direction_change > 0.5:  # 显著方向变化
                        direction_changes += 1
                prev_direction = direction
        
        total_length += path_length
        total_curvature += path_curvature
        total_points += len(points)
    
    # 计算特征
    avg_curvature = total_curvature / max(total_points, 1)
    complexity = min(1.0, total_length / 1000.0)  # 归一化复杂度
    rhythm = min(1.0, direction_changes / max(total_points / 10, 1))  # 节奏感
    smoothness = max(0.0, 1.0 - avg_curvature)  # 平滑度
    
    return {
        "dominant_curves": avg_curvature > 0.3,
        "complexity": complexity,
        "rhythm": rhythm,
        "total_length": total_length,
        "smoothness": smoothness,
        "curvature": avg_curvature,
        "direction_changes": direction_changes
    }

def calculate_similarity(user_features: Dict[str, Any], artwork_features: Dict[str, Any]) -> float:
    """计算用户线条与名画的相似度"""
    similarity = 0.0
    weight_sum = 0.0
    
    # 曲线特征匹配
    curves_match = user_features.get("dominant_curves", False) == artwork_features.get("dominant_curves", False)
    similarity += 0.3 * (1.0 if curves_match else 0.0)
    weight_sum += 0.3
    
    # 复杂度匹配
    user_complexity = user_features.get("complexity", 0.0)
    artwork_complexity = artwork_features.get("complexity", 0.0)
    complexity_diff = abs(user_complexity - artwork_complexity)
    complexity_similarity = max(0.0, 1.0 - complexity_diff)
    similarity += 0.25 * complexity_similarity
    weight_sum += 0.25
    
    # 节奏感匹配
    user_rhythm = user_features.get("rhythm", 0.0)
    artwork_rhythm = artwork_features.get("rhythm", 0.0)
    rhythm_diff = abs(user_rhythm - artwork_rhythm)
    rhythm_similarity = max(0.0, 1.0 - rhythm_diff)
    similarity += 0.25 * rhythm_similarity
    weight_sum += 0.25
    
    return similarity / max(weight_sum, 1.0) if weight_sum > 0 else 0.0

def find_best_matching_artwork(user_features: Dict[str, Any]) -> Dict[str, Any]:
    """找到最匹配的名画"""
    best_match = None
    best_similarity = 0.0
    
    for artwork in FAMOUS_ARTWORKS:
        similarity = calculate_similarity(user_features, artwork["line_features"])
        if similarity > best_similarity:
            best_similarity = similarity
            best_match = artwork
    
    return {
        "artwork": best_match,
        "similarity": best_similarity
    }

def generate_suggestions(user_features: Dict[str, Any], matched_artwork: Dict[str, Any]) -> List[str]:
    """根据分析结果生成改进建议"""
    suggestions = []
    
    # 基于复杂度的建议
    complexity = user_features.get("complexity", 0.0)
    if complexity < 0.3:
        suggestions.append("尝试绘制更多的线条来增加作品的丰富度")
    elif complexity > 0.8:
        suggestions.append("可以适当简化线条，突出主要特征")
    
    # 基于曲线特征的建议
    if user_features.get("dominant_curves", False):
        suggestions.append("您的线条很有流动感！可以尝试更多曲线变化")
    else:
        suggestions.append("尝试加入一些曲线元素，让作品更有动感")
    
    # 确保至少有一条建议
    if not suggestions:
        suggestions.append("继续练习，您的绘画技巧会越来越好！")
    
    return suggestions[:3]  # 最多返回3条建议

@app.get("/")
async def root():
    return {"message": "MemoryBrush API is running!"}

@app.post("/api/games/analyze-lines", response_model=LineAnalysisResult)
async def analyze_user_lines(request: LineAnalysisRequest):
    """分析用户绘制的线条并匹配名画"""
    try:
        # 分析用户线条特征
        user_features = analyze_line_features(request.paths)
        
        # 找到最匹配的名画
        match_result = find_best_matching_artwork(user_features)
        matched_artwork = match_result["artwork"]
        similarity_score = match_result["similarity"]
        
        # 生成建议
        suggestions = generate_suggestions(user_features, matched_artwork)
        
        result = LineAnalysisResult(
            matched_artwork=MatchedArtwork(**{
                "id": matched_artwork["id"],
                "title": matched_artwork["title"],
                "artist": matched_artwork["artist"],
                "year": matched_artwork["year"],
                "style": matched_artwork["style"],
                "description": matched_artwork["description"],
                "image_url": matched_artwork["image_url"]
            }),
            similarity_score=similarity_score,
            line_features=LineFeatures(**user_features),
            suggestions=suggestions
        )
        
        return result
        
    except Exception as e:
        print(f"线条分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail="线条分析失败")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
