"""
游戏相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import base64
import io
import numpy as np
from PIL import Image
import cv2
import math
import json
import os
import uuid

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.ai_image_service import ai_image_service
from app.services.line_extraction_service import line_extraction_service
from app.core.config import settings

logger = get_logger("games_api")
router = APIRouter()


# Pydantic模型
class GameLevel(BaseModel):
    """游戏级别模型"""
    level: int
    name: str
    description: str
    stages: List[str]
    difficulty: str
    estimated_time: int  # 预估完成时间（分钟）


class GameSession(BaseModel):
    """游戏会话模型"""
    session_id: str
    user_id: Optional[str] = None
    level: int
    stage: int
    start_time: str
    status: str  # "active", "completed", "paused"


class DrawingData(BaseModel):
    """绘画数据模型"""
    canvas_data: str  # Base64编码的画布数据
    strokes: List[dict]  # 笔画数据
    drawing_time: int  # 绘画时间（秒）
    brush_settings: dict  # 画笔设置


class LineAnalysisRequest(BaseModel):
    """线条分析请求模型"""
    canvas_data: str  # Base64编码的画布数据
    paths: List[Dict[str, Any]]  # 路径数据，包含点坐标、颜色、大小等


class LineAnalysisResult(BaseModel):
    """线条分析结果模型"""
    matched_artwork: Dict[str, Any]  # 匹配的名画信息
    similarity_score: float  # 相似度分数 (0-1)
    line_features: Dict[str, Any]  # 线条特征分析
    suggestions: List[str]  # 改进建议
    ai_generated_image: Optional[Dict[str, Any]] = None  # AI生成的图像信息


class TraceRequest(BaseModel):
    """描线练习请求模型"""
    artwork_name: str
    difficulty: str = "easy"  # easy, medium, hard


class TraceSubmission(BaseModel):
    """描线提交模型"""
    session_id: str
    user_paths: List[Dict[str, Any]]
    completed_path_id: str


class TraceProgress(BaseModel):
    """描线进度模型"""
    completed_paths: int
    total_paths: int
    accuracy: float
    current_path: int


# 游戏级别配置
GAME_LEVELS = [
    {
        "level": 1,
        "name": "简单线条",
        "description": "学习基础的线条绘制",
        "stages": [
            "自由画线",
            "描摹练习",
            "几何图形绘制"
        ],
        "difficulty": "简单",
        "estimated_time": 15
    },
    {
        "level": 2,
        "name": "二维转三维",
        "description": "将平面图形转换为立体图形",
        "stages": [
            "基础立体图形",
            "色彩填充",
            "质感绘制"
        ],
        "difficulty": "中等",
        "estimated_time": 25
    },
    {
        "level": 3,
        "name": "引导创作",
        "description": "在引导下完成完整画面",
        "stages": [
            "抽象艺术",
            "几何静物",
            "生活物品"
        ],
        "difficulty": "中等",
        "estimated_time": 35
    },
    {
        "level": 4,
        "name": "照片艺术化",
        "description": "将照片转换为艺术作品",
        "stages": [
            "轮廓提取",
            "风格选择",
            "智能渲染"
        ],
        "difficulty": "困难",
        "estimated_time": 45
    }
]


@router.get("/levels", response_model=List[GameLevel])
async def get_game_levels():
    """获取所有游戏级别"""
    logger.info("获取游戏级别列表")
    return GAME_LEVELS


@router.get("/levels/{level_id}", response_model=GameLevel)
async def get_game_level(level_id: int):
    """获取指定级别的详细信息"""
    logger.info(f"获取级别 {level_id} 的详细信息")

    for level in GAME_LEVELS:
        if level["level"] == level_id:
            return level

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"级别 {level_id} 不存在"
    )


@router.post("/sessions/start")
async def start_game_session(
    level: int,
    stage: int = 1,
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """开始新的游戏会话"""
    logger.info(f"开始游戏会话: 级别 {level}, 阶段 {stage}")

    # 验证级别是否存在
    if level < 1 or level > len(GAME_LEVELS):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的游戏级别"
        )

    # 这里应该创建数据库记录
    # 暂时返回模拟数据
    session_data = {
        "session_id": f"session_{level}_{stage}_{user_id or 'anonymous'}",
        "user_id": user_id,
        "level": level,
        "stage": stage,
        "start_time": "2024-01-01T00:00:00Z",
        "status": "active"
    }

    return {
        "success": True,
        "message": "游戏会话已开始",
        "data": session_data
    }


@router.post("/sessions/{session_id}/submit")
async def submit_drawing(
    session_id: str,
    drawing_data: DrawingData,
    db: Session = Depends(get_db)
):
    """提交绘画作品"""
    logger.info(f"提交绘画作品: 会话 {session_id}")

    # 这里应该保存绘画数据到数据库
    # 并进行认知评估分析

    # 模拟评估结果
    assessment_result = {
        "score": 85,
        "completion_time": drawing_data.drawing_time,
        "accuracy": 0.9,
        "creativity": 0.8,
        "motor_skills": 0.85,
        "cognitive_load": 0.7,
        "feedback": "很好的作品！线条流畅，创意丰富。",
        "suggestions": [
            "可以尝试更多的色彩搭配",
            "注意保持线条的连贯性"
        ]
    }

    return {
        "success": True,
        "message": "作品提交成功",
        "assessment": assessment_result
    }


@router.get("/sessions/{session_id}/progress")
async def get_session_progress(
    session_id: str,
    db: Session = Depends(get_db)
):
    """获取会话进度"""
    logger.info(f"获取会话进度: {session_id}")

    # 模拟进度数据
    progress_data = {
        "session_id": session_id,
        "current_level": 1,
        "current_stage": 2,
        "completed_stages": 1,
        "total_stages": 3,
        "progress_percentage": 33.3,
        "time_spent": 600,  # 秒
        "achievements": [
            {
                "id": "first_drawing",
                "name": "初次绘画",
                "description": "完成第一幅作品",
                "earned_at": "2024-01-01T00:10:00Z"
            }
        ]
    }

    return {
        "success": True,
        "data": progress_data
    }


@router.get("/templates/{level}/{stage}")
async def get_drawing_template(level: int, stage: int):
    """获取绘画模板"""
    logger.info(f"获取绘画模板: 级别 {level}, 阶段 {stage}")

    # 模拟模板数据
    template_data = {
        "template_id": f"template_{level}_{stage}",
        "level": level,
        "stage": stage,
        "type": "line_drawing" if level == 1 else "complex_drawing",
        "instructions": "请按照虚线描绘图形",
        "template_image": "/static/templates/level1_stage1.png",
        "guide_points": [
            {"x": 100, "y": 100},
            {"x": 200, "y": 150},
            {"x": 300, "y": 100}
        ],
        "expected_completion_time": 300  # 秒
    }

    return {
        "success": True,
        "data": template_data
    }


@router.get("/leaderboard")
async def get_leaderboard(
    level: Optional[int] = None,
    limit: int = 10
):
    """获取排行榜"""
    logger.info(f"获取排行榜: 级别 {level}, 限制 {limit}")

    # 模拟排行榜数据
    leaderboard_data = [
        {
            "rank": 1,
            "user_name": "艺术大师",
            "score": 95,
            "level": level or 1,
            "completion_time": 180,
            "achievements_count": 5
        },
        {
            "rank": 2,
            "user_name": "创意画家",
            "score": 92,
            "level": level or 1,
            "completion_time": 200,
            "achievements_count": 4
        }
    ]

    return {
        "success": True,
        "data": leaderboard_data
    }


# 世界名画数据库（简化版）
FAMOUS_ARTWORKS = [
    {
        "id": "starry_night",
        "title": "星夜",
        "artist": "文森特·梵高",
        "year": 1889,
        "style": "后印象派",
        "description": "梵高最著名的作品之一，以其独特的旋涡状笔触和鲜艳的色彩而闻名。",
        "image_url": "/static/artworks/starry_night.jpg",
        "line_features": {
            "dominant_curves": True,
            "spiral_patterns": True,
            "flowing_lines": True,
            "complexity": 0.8,
            "rhythm": 0.9
        }
    },
    {
        "id": "great_wave",
        "title": "神奈川冲浪里",
        "artist": "葛饰北斋",
        "year": 1831,
        "style": "浮世绘",
        "description": "日本最著名的浮世绘作品，以其动态的波浪线条而闻名。",
        "image_url": "/static/artworks/great_wave.jpg",
        "line_features": {
            "dominant_curves": True,
            "wave_patterns": True,
            "flowing_lines": True,
            "complexity": 0.7,
            "rhythm": 0.8
        }
    },
    {
        "id": "mona_lisa",
        "title": "蒙娜丽莎",
        "artist": "列奥纳多·达·芬奇",
        "year": 1503,
        "style": "文艺复兴",
        "description": "世界上最著名的肖像画，以其神秘的微笑和精细的线条而闻名。",
        "image_url": "/static/artworks/mona_lisa.jpg",
        "line_features": {
            "dominant_curves": False,
            "smooth_lines": True,
            "detailed_lines": True,
            "complexity": 0.6,
            "rhythm": 0.5
        }
    },
    {
        "id": "scream",
        "title": "呐喊",
        "artist": "爱德华·蒙克",
        "year": 1893,
        "style": "表现主义",
        "description": "表现主义的代表作，以其扭曲的线条和强烈的情感表达而闻名。",
        "image_url": "/static/artworks/scream.jpg",
        "line_features": {
            "dominant_curves": True,
            "wavy_lines": True,
            "distorted_lines": True,
            "complexity": 0.7,
            "rhythm": 0.6
        }
    },
    {
        "id": "guernica",
        "title": "格尔尼卡",
        "artist": "巴勃罗·毕加索",
        "year": 1937,
        "style": "立体主义",
        "description": "毕加索的反战杰作，以其几何化的线条和抽象的形式而闻名。",
        "image_url": "/static/artworks/guernica.jpg",
        "line_features": {
            "dominant_curves": False,
            "angular_lines": True,
            "geometric_patterns": True,
            "complexity": 0.9,
            "rhythm": 0.4
        }
    }
]


def analyze_line_features(paths: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析用户绘制的线条特征"""
    if not paths:
        return {
            "dominant_curves": False,
            "complexity": 0.0,
            "rhythm": 0.0,
            "total_length": 0.0,
            "smoothness": 0.0
        }

    total_length = 0.0
    total_curvature = 0.0
    direction_changes = 0
    total_points = 0

    for path in paths:
        points = path.get("points", [])
        if len(points) < 2:
            continue

        # 计算路径长度和曲率
        path_length = 0.0
        path_curvature = 0.0
        prev_direction = None

        for i in range(1, len(points)):
            # 计算距离
            dx = points[i]["x"] - points[i - 1]["x"]
            dy = points[i]["y"] - points[i - 1]["y"]
            distance = math.sqrt(dx * dx + dy * dy)
            path_length += distance

            # 计算方向变化
            if distance > 0:
                direction = math.atan2(dy, dx)
                if prev_direction is not None:
                    direction_change = abs(direction - prev_direction)
                    if direction_change > math.pi:
                        direction_change = 2 * math.pi - direction_change
                    path_curvature += direction_change
                    if direction_change > 0.5:  # 显著方向变化
                        direction_changes += 1
                prev_direction = direction

        total_length += path_length
        total_curvature += path_curvature
        total_points += len(points)

    # 计算特征
    avg_curvature = total_curvature / max(total_points, 1)
    complexity = min(1.0, total_length / 1000.0)  # 归一化复杂度
    rhythm = min(1.0, direction_changes / max(total_points / 10, 1))  # 节奏感
    smoothness = max(0.0, 1.0 - avg_curvature)  # 平滑度

    return {
        "dominant_curves": avg_curvature > 0.3,
        "complexity": complexity,
        "rhythm": rhythm,
        "total_length": total_length,
        "smoothness": smoothness,
        "curvature": avg_curvature,
        "direction_changes": direction_changes
    }


def calculate_similarity(user_features: Dict[str, Any], artwork_features: Dict[str, Any]) -> float:
    """计算用户线条与名画的相似度"""
    similarity = 0.0
    weight_sum = 0.0

    # 曲线特征匹配
    curves_match = user_features.get("dominant_curves", False) == artwork_features.get("dominant_curves", False)
    similarity += 0.3 * (1.0 if curves_match else 0.0)
    weight_sum += 0.3

    # 复杂度匹配
    user_complexity = user_features.get("complexity", 0.0)
    artwork_complexity = artwork_features.get("complexity", 0.0)
    complexity_diff = abs(user_complexity - artwork_complexity)
    complexity_similarity = max(0.0, 1.0 - complexity_diff)
    similarity += 0.25 * complexity_similarity
    weight_sum += 0.25

    # 节奏感匹配
    user_rhythm = user_features.get("rhythm", 0.0)
    artwork_rhythm = artwork_features.get("rhythm", 0.0)
    rhythm_diff = abs(user_rhythm - artwork_rhythm)
    rhythm_similarity = max(0.0, 1.0 - rhythm_diff)
    similarity += 0.25 * rhythm_similarity
    weight_sum += 0.25

    # 平滑度匹配
    user_smoothness = user_features.get("smoothness", 0.0)
    artwork_smoothness = artwork_features.get("smooth_lines", False)
    if isinstance(artwork_smoothness, bool):
        artwork_smoothness = 0.8 if artwork_smoothness else 0.2
    smoothness_diff = abs(user_smoothness - artwork_smoothness)
    smoothness_similarity = max(0.0, 1.0 - smoothness_diff)
    similarity += 0.2 * smoothness_similarity
    weight_sum += 0.2

    return similarity / max(weight_sum, 1.0) if weight_sum > 0 else 0.0


def find_best_matching_artwork(user_features: Dict[str, Any]) -> Dict[str, Any]:
    """找到最匹配的名画"""
    best_match = None
    best_similarity = 0.0

    for artwork in FAMOUS_ARTWORKS:
        similarity = calculate_similarity(user_features, artwork["line_features"])
        if similarity > best_similarity:
            best_similarity = similarity
            best_match = artwork

    return {
        "artwork": best_match,
        "similarity": best_similarity
    }


@router.post("/analyze-lines", response_model=LineAnalysisResult)
async def analyze_user_lines(request: LineAnalysisRequest):
    """分析用户绘制的线条并匹配名画"""
    logger.info("开始分析用户线条")

    try:
        # 分析用户线条特征
        user_features = analyze_line_features(request.paths)
        logger.info(f"用户线条特征: {user_features}")

        # 找到最匹配的名画
        match_result = find_best_matching_artwork(user_features)
        matched_artwork = match_result["artwork"]
        similarity_score = match_result["similarity"]

        # 生成建议
        suggestions = generate_suggestions(user_features, matched_artwork)

        # 先返回分析结果，AI生图状态设为generating
        ai_generated_image = {
            "status": "generating",
            "style": matched_artwork["style"],
            "prompt": None  # 稍后生成
        }

        result = LineAnalysisResult(
            matched_artwork={
                "id": matched_artwork["id"],
                "title": matched_artwork["title"],
                "artist": matched_artwork["artist"],
                "year": matched_artwork["year"],
                "style": matched_artwork["style"],
                "description": matched_artwork["description"],
                "image_url": matched_artwork["image_url"]
            },
            similarity_score=similarity_score,
            line_features=user_features,
            suggestions=suggestions,
            ai_generated_image=ai_generated_image
        )

        logger.info(f"匹配结果: {matched_artwork['title']} (相似度: {similarity_score:.2f})")
        return result

    except Exception as e:
        logger.error(f"线条分析失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="线条分析失败"
        )


@router.post("/generate-ai-image")
async def generate_ai_image(request: LineAnalysisRequest):
    """异步生成AI艺术作品"""
    logger.info("开始异步生成AI艺术作品")

    try:
        # 重新分析线条特征（或者可以从前端传入）
        user_features = analyze_line_features(request.paths)

        # 找到最匹配的名画
        match_result = find_best_matching_artwork(user_features)
        matched_artwork = match_result["artwork"]

        # 生成AI艺术作品
        ai_result = await ai_image_service.generate_artwork_from_lines(
            canvas_data=request.canvas_data,
            paths=request.paths,
            style=matched_artwork["style"],
            matched_artwork=matched_artwork
        )

        if ai_result.get("success"):
            return {
                "success": True,
                "image_url": ai_result["generated_image_url"],
                "style": ai_result["style"],
                "prompt": ai_result["prompt"],
                "status": "success"
            }
        else:
            return {
                "success": False,
                "status": "failed",
                "error": ai_result.get("error", "未知错误")
            }

    except Exception as e:
        logger.error(f"AI生图异常: {str(e)}")
        return {
            "success": False,
            "status": "failed",
            "error": str(e)
        }


def generate_suggestions(user_features: Dict[str, Any], matched_artwork: Dict[str, Any]) -> List[str]:
    """根据分析结果生成改进建议"""
    suggestions = []

    # 基于复杂度的建议
    complexity = user_features.get("complexity", 0.0)
    if complexity < 0.3:
        suggestions.append("尝试绘制更多的线条来增加作品的丰富度")
    elif complexity > 0.8:
        suggestions.append("可以适当简化线条，突出主要特征")

    # 基于曲线特征的建议
    if user_features.get("dominant_curves", False):
        suggestions.append("您的线条很有流动感！可以尝试更多曲线变化")
    else:
        suggestions.append("尝试加入一些曲线元素，让作品更有动感")

    # 基于节奏感的建议
    rhythm = user_features.get("rhythm", 0.0)
    if rhythm < 0.3:
        suggestions.append("可以尝试在线条中加入更多的节奏变化")

    # 基于匹配的名画风格的建议
    if matched_artwork:
        style = matched_artwork.get("style", "")
        if "印象派" in style:
            suggestions.append("您的风格接近印象派！可以尝试更多短促的笔触")
        elif "立体主义" in style:
            suggestions.append("您的线条有几何感！可以尝试更多角度变化")
        elif "表现主义" in style:
            suggestions.append("您的线条很有表现力！可以更大胆地表达情感")

    # 确保至少有一条建议
    if not suggestions:
        suggestions.append("继续练习，您的绘画技巧会越来越好！")

    return suggestions[:3]  # 最多返回3条建议


@router.get("/artworks", response_model=List[Dict[str, Any]])
async def get_famous_artworks():
    """获取所有名画列表"""
    logger.info("获取名画列表")
    return [
        {
            "id": artwork["id"],
            "title": artwork["title"],
            "artist": artwork["artist"],
            "year": artwork["year"],
            "style": artwork["style"],
            "description": artwork["description"],
            "image_url": artwork["image_url"]
        }
        for artwork in FAMOUS_ARTWORKS
    ]


@router.get("/artworks/{artwork_id}")
async def get_artwork_details(artwork_id: str):
    """获取指定名画的详细信息"""
    logger.info(f"获取名画详情: {artwork_id}")

    for artwork in FAMOUS_ARTWORKS:
        if artwork["id"] == artwork_id:
            return {
                "success": True,
                "data": artwork
            }

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"名画 {artwork_id} 不存在"
    )


async def start_custom_trace_practice(session_id: str):
    """开始自定义轨迹练习"""
    logger.info(f"开始自定义轨迹练习: {session_id}")

    try:
        # 获取轨迹会话数据
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        session_file = os.path.join(traces_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"自定义轨迹 {session_id} 不存在"
            )

        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)

        # 转换轨迹数据为引导线格式
        guide_paths = []
        for i, stroke in enumerate(session_data.get("strokes", [])):
            guide_path = {
                "id": f"path_{i}",
                "points": [{"x": p["x"], "y": p["y"]} for p in stroke["points"]],
                "color": "#FFD700",  # 金黄色引导线
                "size": 3,
                "order": stroke.get("order", i)
            }
            guide_paths.append(guide_path)

        # 按顺序排序
        guide_paths.sort(key=lambda x: x["order"])

        # 生成游戏会话ID
        game_session_id = str(uuid.uuid4())

        return {
            "success": True,
            "session_id": game_session_id,
            "guide_paths": guide_paths,
            "guide_image": session_data.get("backgroundImage", ""),
            "artwork_name": session_data.get("name", "自定义轨迹"),
            "canvas_size": session_data.get("canvasSize", {"width": 800, "height": 600}),
            "total_paths": len(guide_paths)
        }

    except Exception as e:
        logger.error(f"开始自定义轨迹练习失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"开始自定义轨迹练习失败: {str(e)}"
        )


@router.post("/trace/start")
async def start_trace_practice(
    request: TraceRequest,
    db: Session = Depends(get_db)
):
    """开始描线练习"""
    logger.info(f"开始描线练习: {request.artwork_name}")

    try:
        # 检查是否是自定义轨迹
        if request.artwork_name.startswith("custom_"):
            # 自定义轨迹格式: custom_<session_id>
            session_id = request.artwork_name.replace("custom_", "")
            return await start_custom_trace_practice(session_id)

        # 获取练习模板（忽略前端传递的difficulty，使用配置中的难度）
        template_data = line_extraction_service.create_practice_template(
            request.artwork_name
        )

        if not template_data["success"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=template_data["error"]
            )

        # 生成会话ID
        import uuid
        session_id = str(uuid.uuid4())

        return {
            "success": True,
            "session_id": session_id,
            "artwork_name": request.artwork_name,
            "guide_paths": template_data["paths"],
            "guide_image": template_data["guide_image"],
            "total_paths": template_data["total_paths"],
            "canvas_size": template_data["canvas_size"],
            "difficulty": request.difficulty
        }

    except Exception as e:
        logger.error(f"开始描线练习失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"开始描线练习失败: {str(e)}"
        )


@router.post("/trace/submit")
async def submit_trace_progress(
    submission: TraceSubmission,
    db: Session = Depends(get_db)
):
    """提交描线进度"""
    logger.info(f"提交描线进度: 会话 {submission.session_id}")

    try:
        # 这里可以添加进度保存逻辑
        # 暂时返回成功响应

        return {
            "success": True,
            "message": "描线进度已保存",
            "session_id": submission.session_id,
            "completed_path": submission.completed_path_id
        }

    except Exception as e:
        logger.error(f"提交描线进度失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交描线进度失败: {str(e)}"
        )


@router.get("/trace/{session_id}/progress")
async def get_trace_progress(
    session_id: str,
    db: Session = Depends(get_db)
):
    """获取描线练习进度"""
    logger.info(f"获取描线进度: {session_id}")

    try:
        # 这里可以从数据库获取实际进度
        # 暂时返回模拟数据

        return {
            "success": True,
            "progress": {
                "completed_paths": 2,
                "total_paths": 5,
                "accuracy": 0.85,
                "current_path": 2
            }
        }

    except Exception as e:
        logger.error(f"获取描线进度失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取描线进度失败: {str(e)}"
        )


@router.get("/trace/config")
async def get_trace_config():
    """获取描线练习配置"""
    try:
        config = settings.get_trace_difficulty_config()
        return {
            "success": True,
            "config": config,
            "difficulty": settings.TRACE_DIFFICULTY
        }

    except Exception as e:
        logger.error(f"获取描线配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取描线配置失败: {str(e)}"
        )
