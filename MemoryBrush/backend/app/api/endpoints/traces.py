"""
轨迹记录相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import json
import uuid
from datetime import datetime
import os

from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger("traces_api")
router = APIRouter()


# Pydantic模型
class Point(BaseModel):
    """点坐标模型"""
    x: float
    y: float


class TraceStroke(BaseModel):
    """轨迹笔画模型"""
    id: str
    points: List[Point]


class TraceSession(BaseModel):
    """轨迹会话模型"""
    id: str
    name: str
    description: Optional[str] = None
    strokes: List[TraceStroke]
    canvasSize: Dict[str, int]
    createdAt: str
    duration: int  # 录制时长（毫秒）


class TraceSessionCreate(BaseModel):
    """创建轨迹会话请求模型"""
    name: str
    description: Optional[str] = None
    strokes: List[TraceStroke]
    canvasSize: Dict[str, int]
    duration: int


class TraceSessionResponse(BaseModel):
    """轨迹会话响应模型"""
    success: bool
    message: str
    data: Optional[TraceSession] = None


class GuidePathPoint(BaseModel):
    """引导路径点模型"""
    x: float
    y: float


class GuidePath(BaseModel):
    """引导路径模型"""
    id: str
    points: List[GuidePathPoint]


class GenerateGuideRequest(BaseModel):
    """生成引导线请求模型"""
    session_id: str
    simplification_level: Optional[str] = "medium"  # low, medium, high
    min_stroke_length: Optional[int] = 20  # 最小笔画长度
    merge_distance: Optional[int] = 50  # 合并距离


class GenerateGuideResponse(BaseModel):
    """生成引导线响应模型"""
    success: bool
    message: str
    guide_paths: Optional[List[GuidePath]] = None
    total_paths: Optional[int] = None
    canvas_size: Optional[Dict[str, int]] = None


@router.post("/sessions", response_model=TraceSessionResponse)
async def create_trace_session(
    session_data: TraceSessionCreate,
    db: Session = Depends(get_db)
):
    """创建新的轨迹会话"""
    logger.info(f"创建轨迹会话: {session_data.name}")

    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 优化轨迹数据：过滤短线和平滑路径
        optimized_strokes = optimize_trace_strokes(session_data.strokes)
        logger.info(f"轨迹优化完成: 原始笔画数 {len(session_data.strokes)}, 优化后笔画数 {len(optimized_strokes)}")

        # 创建轨迹会话对象
        trace_session = TraceSession(
            id=session_id,
            name=session_data.name,
            description=session_data.description,
            strokes=optimized_strokes,
            canvasSize=session_data.canvasSize,
            createdAt=datetime.now().isoformat(),
            duration=session_data.duration
        )

        # 保存到文件系统（暂时不使用数据库）
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        os.makedirs(traces_dir, exist_ok=True)

        session_file = os.path.join(traces_dir, f"{session_id}.json")
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(trace_session.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"轨迹会话已保存: {session_file}")

        return TraceSessionResponse(
            success=True,
            message="轨迹会话创建成功",
            data=trace_session
        )

    except Exception as e:
        logger.error(f"创建轨迹会话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建轨迹会话失败: {str(e)}"
        )


@router.get("/sessions", response_model=List[TraceSession])
async def get_trace_sessions(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """获取轨迹会话列表"""
    logger.info("获取轨迹会话列表")

    try:
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        if not os.path.exists(traces_dir):
            return []

        sessions = []
        session_files = [f for f in os.listdir(traces_dir) if f.endswith('.json')]

        # 按创建时间排序
        session_files.sort(key=lambda x: os.path.getctime(os.path.join(traces_dir, x)), reverse=True)

        # 应用分页
        start_idx = offset
        end_idx = offset + limit

        for session_file in session_files[start_idx:end_idx]:
            session_path = os.path.join(traces_dir, session_file)
            try:
                with open(session_path, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                    sessions.append(TraceSession(**session_data))
            except Exception as e:
                logger.warning(f"读取会话文件失败 {session_file}: {str(e)}")
                continue

        return sessions

    except Exception as e:
        logger.error(f"获取轨迹会话列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取轨迹会话列表失败: {str(e)}"
        )


@router.get("/sessions/{session_id}", response_model=TraceSession)
async def get_trace_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """获取指定的轨迹会话"""
    logger.info(f"获取轨迹会话: {session_id}")

    try:
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        session_file = os.path.join(traces_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"轨迹会话 {session_id} 不存在"
            )

        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
            return TraceSession(**session_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取轨迹会话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取轨迹会话失败: {str(e)}"
        )


@router.delete("/sessions/{session_id}")
async def delete_trace_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """删除轨迹会话"""
    logger.info(f"删除轨迹会话: {session_id}")

    try:
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        session_file = os.path.join(traces_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"轨迹会话 {session_id} 不存在"
            )

        os.remove(session_file)
        logger.info(f"轨迹会话已删除: {session_file}")

        return {
            "success": True,
            "message": "轨迹会话删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除轨迹会话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除轨迹会话失败: {str(e)}"
        )


@router.post("/sessions/{session_id}/generate-guide", response_model=GenerateGuideResponse)
async def generate_guide_paths(
    session_id: str,
    request: GenerateGuideRequest,
    db: Session = Depends(get_db)
):
    """从轨迹会话生成引导线"""
    logger.info(f"为会话 {session_id} 生成引导线")

    try:
        # 获取轨迹会话
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        session_file = os.path.join(traces_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"轨迹会话 {session_id} 不存在"
            )

        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
            trace_session = TraceSession(**session_data)

        # 处理轨迹数据生成引导线
        guide_paths = process_strokes_to_guide_paths(trace_session.strokes)

        return GenerateGuideResponse(
            success=True,
            message="引导线生成成功",
            guide_paths=guide_paths,
            total_paths=len(guide_paths),
            canvas_size=trace_session.canvasSize
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成引导线失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成引导线失败: {str(e)}"
        )


@router.get("/sessions/{session_id}/for-game", response_model=GenerateGuideResponse)
async def get_trace_for_game(
    session_id: str,
    db: Session = Depends(get_db)
):
    """获取轨迹数据用于游戏关卡"""
    logger.info(f"获取游戏轨迹数据: {session_id}")

    try:
        # 获取轨迹会话
        traces_dir = os.path.join(settings.DATA_DIR, "custom_traces")
        session_file = os.path.join(traces_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"轨迹会话 {session_id} 不存在"
            )

        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
            trace_session = TraceSession(**session_data)

        # 生成引导线
        guide_paths = process_strokes_to_guide_paths(trace_session.strokes)

        return GenerateGuideResponse(
            success=True,
            message="游戏轨迹数据获取成功",
            guide_paths=guide_paths,
            total_paths=len(guide_paths),
            canvas_size=trace_session.canvasSize
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取游戏轨迹数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取游戏轨迹数据失败: {str(e)}"
        )


def optimize_trace_strokes(strokes: List[TraceStroke], min_length_pixels: int = 3) -> List[TraceStroke]:
    """
    优化轨迹笔画：过滤短线、单点，平滑路径以减少抖动

    Args:
        strokes: 原始笔画列表
        min_length_pixels: 最小笔画长度（像素）

    Returns:
        优化后的笔画列表
    """
    optimized_strokes = []

    for stroke in strokes:
        if len(stroke.points) < 2:
            # 过滤掉单点
            continue

        # 计算笔画总长度
        total_length = calculate_stroke_length(stroke.points)

        if total_length < min_length_pixels:
            # 过滤掉短线
            continue

        # 只对较长的路径进行优化，保持短路径的原始细节
        if len(stroke.points) < 10:
            # 短路径保持原样
            optimized_strokes.append(stroke)
        else:
            # 对长路径进行轻度平滑和简化
            smoothed_points = smooth_stroke_points(stroke.points)
            simplified_points = simplify_stroke_points(smoothed_points)

            if len(simplified_points) >= 2:
                optimized_stroke = TraceStroke(
                    id=stroke.id,
                    points=simplified_points
                )
                optimized_strokes.append(optimized_stroke)

    return optimized_strokes


def calculate_stroke_length(points: List[Point]) -> float:
    """计算笔画的总长度"""
    if len(points) < 2:
        return 0.0

    total_length = 0.0
    for i in range(1, len(points)):
        dx = points[i].x - points[i-1].x
        dy = points[i].y - points[i-1].y
        total_length += (dx * dx + dy * dy) ** 0.5

    return total_length


def smooth_stroke_points(points: List[Point]) -> List[Point]:
    """
    使用轻度平滑减少手抖，保持轨迹细节

    Args:
        points: 原始点列表
        window_size: 平滑窗口大小（减小以保持更多细节）

    Returns:
        平滑后的点列表
    """
    if len(points) <= 3:
        return points  # 对于很短的路径，不进行平滑

    smoothed_points = []

    # 保留起点
    smoothed_points.append(points[0])

    # 对中间点进行轻度平滑
    for i in range(1, len(points) - 1):
        prev_point = points[i - 1]
        curr_point = points[i]
        next_point = points[i + 1]

        # 使用加权平均，当前点权重更大
        smoothed_x = (prev_point.x + curr_point.x * 2 + next_point.x) / 4
        smoothed_y = (prev_point.y + curr_point.y * 2 + next_point.y) / 4

        smoothed_point = Point(x=smoothed_x, y=smoothed_y)
        smoothed_points.append(smoothed_point)

    # 保留终点
    smoothed_points.append(points[-1])

    return smoothed_points


def simplify_stroke_points(points: List[Point], tolerance: float = 0.5) -> List[Point]:
    """
    使用Douglas-Peucker算法简化路径，移除冗余点

    Args:
        points: 原始点列表
        tolerance: 简化容差

    Returns:
        简化后的点列表
    """
    if len(points) <= 2:
        return points

    def perpendicular_distance(point: Point, line_start: Point, line_end: Point) -> float:
        """计算点到线段的垂直距离"""
        if line_start.x == line_end.x and line_start.y == line_end.y:
            dx = point.x - line_start.x
            dy = point.y - line_start.y
            return (dx * dx + dy * dy) ** 0.5

        # 线段长度的平方
        line_length_sq = ((line_end.x - line_start.x) ** 2 +
                         (line_end.y - line_start.y) ** 2)

        if line_length_sq == 0:
            dx = point.x - line_start.x
            dy = point.y - line_start.y
            return (dx * dx + dy * dy) ** 0.5

        # 计算投影参数
        t = ((point.x - line_start.x) * (line_end.x - line_start.x) +
             (point.y - line_start.y) * (line_end.y - line_start.y)) / line_length_sq

        # 限制t在[0,1]范围内
        t = max(0, min(1, t))

        # 计算投影点
        proj_x = line_start.x + t * (line_end.x - line_start.x)
        proj_y = line_start.y + t * (line_end.y - line_start.y)

        # 返回距离
        dx = point.x - proj_x
        dy = point.y - proj_y
        return (dx * dx + dy * dy) ** 0.5

    def douglas_peucker(points_list: List[Point], start: int, end: int) -> List[Point]:
        """递归执行Douglas-Peucker算法"""
        if end - start <= 1:
            return [points_list[start], points_list[end]]

        max_distance = 0
        max_index = start

        # 找到距离线段最远的点
        for i in range(start + 1, end):
            distance = perpendicular_distance(points_list[i], points_list[start], points_list[end])
            if distance > max_distance:
                max_distance = distance
                max_index = i

        # 如果最大距离小于容差，直接返回端点
        if max_distance < tolerance:
            return [points_list[start], points_list[end]]

        # 递归处理两段
        left_points = douglas_peucker(points_list, start, max_index)
        right_points = douglas_peucker(points_list, max_index, end)

        # 合并结果（去除重复的中间点）
        return left_points[:-1] + right_points

    return douglas_peucker(points, 0, len(points) - 1)


def process_strokes_to_guide_paths(strokes: List[TraceStroke]) -> List[GuidePath]:
    """将轨迹笔画转换为引导路径"""

    guide_paths = []

    for i, stroke in enumerate(strokes):
        if len(stroke.points) < 2:
            continue

        # 直接使用优化后的点数据
        guide_points = [
            GuidePathPoint(x=point.x, y=point.y)
            for point in stroke.points
        ]

        guide_path = GuidePath(
            id=f"guide_path_{i}",
            points=guide_points
        )

        guide_paths.append(guide_path)

    return guide_paths
