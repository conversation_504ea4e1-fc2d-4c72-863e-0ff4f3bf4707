"""
线条抽取服务
从图片中提取主要线条，用于描线练习
"""

import cv2
import numpy as np
import base64
import io
import os
from PIL import Image
from typing import List, Dict, Any, Tuple, Optional


from app.core.logging import get_logger
from app.core.config import settings
from .image_line_extractor import image_line_extractor

logger = get_logger("line_extraction_service")


class LineExtractionService:
    """线条抽取服务"""

    def __init__(self):
        self.canvas_width = 800
        self.canvas_height = 600

    def extract_lines_from_image(self, image_path: str) -> Dict[str, Any]:
        """
        从图片中抽取主要线条

        Args:
            image_path: 图片路径

        Returns:
            包含线条数据的字典
        """
        try:
            logger.info(f"开始从图片抽取线条: {image_path}")

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Canny边缘检测
            edges = cv2.Canny(blurred, 50, 150, apertureSize=3)

            # 形态学操作连接断开的线条
            kernel = np.ones((3, 3), np.uint8)
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # 提取轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤和简化轮廓
            simplified_paths = self._process_contours(list(contours), image.shape)

            # 生成引导线条图像
            guide_image = self._create_guide_image(simplified_paths)

            return {
                "success": True,
                "paths": simplified_paths,
                "guide_image": guide_image,
                "total_paths": len(simplified_paths),
                "canvas_size": {"width": self.canvas_width, "height": self.canvas_height}
            }

        except Exception as e:
            logger.error(f"线条抽取失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "paths": [],
                "guide_image": None
            }

    def _process_contours(self, contours: List, image_shape: Tuple) -> List[Dict[str, Any]]:
        """处理和简化轮廓"""
        paths = []
        h, w = image_shape[:2]

        # 按轮廓长度排序，保留主要轮廓
        contours = sorted(contours, key=cv2.contourArea, reverse=True)

        for i, contour in enumerate(contours[:10]):  # 最多保留10条主要线条
            # 简化轮廓
            epsilon = 0.02 * cv2.arcLength(contour, True)
            simplified = cv2.approxPolyDP(contour, epsilon, True)

            # 转换为相对坐标
            points = []
            for point in simplified:
                x = int(point[0][0] * self.canvas_width / w)
                y = int(point[0][1] * self.canvas_height / h)
                points.append({"x": x, "y": y})

            if len(points) >= 3:  # 至少3个点才构成有效路径
                paths.append({
                    "id": f"guide_path_{i}",
                    "points": points,
                    "color": "#cccccc",  # 引导线颜色
                    "size": 2,
                    "order": i  # 绘制顺序
                })

        return paths

    def _create_guide_image(self, paths: List[Dict[str, Any]]) -> Optional[str]:
        """创建引导线条图像"""
        try:
            # 创建白色背景图像
            image = Image.new('RGB', (self.canvas_width, self.canvas_height), 'white')

            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 绘制引导线条
            for path in paths:
                points = path.get("points", [])
                if len(points) >= 2:
                    # 转换点格式
                    cv_points = [(point["x"], point["y"]) for point in points]

                    # 绘制线条
                    for i in range(len(cv_points) - 1):
                        cv2.line(cv_image, cv_points[i], cv_points[i + 1],
                                 (200, 200, 200), 2)  # 浅灰色引导线

            # 转换回PIL格式
            pil_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))

            # 转换为base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{image_base64}"

        except Exception as e:
            logger.error(f"创建引导图像失败: {str(e)}")
            return None

    def create_practice_template(self, artwork_name: str, difficulty: Optional[str] = None) -> Dict[str, Any]:
        """
        为指定名画创建描线练习模板

        Args:
            artwork_name: 名画名称

        Returns:
            练习模板数据
        """
        try:
            # 使用配置中的难度，如果没有传入difficulty参数
            actual_difficulty = difficulty if difficulty is not None else settings.TRACE_DIFFICULTY

            # 特殊处理：如果是sample.png或lotus，使用图像线条抽取
            if artwork_name.lower() in ["sample", "sample.png", "lotus"]:
                return self._create_sample_template(actual_difficulty)

            # 根据名画名称和难度获取对应的简化线条
            template_paths = self._get_artwork_template(artwork_name, actual_difficulty)

            if not template_paths:
                return {
                    "success": False,
                    "error": f"未找到 {artwork_name} 难度 {actual_difficulty} 的练习模板"
                }

            # 生成引导图像
            guide_image = self._create_guide_image(template_paths)

            return {
                "success": True,
                "artwork_name": artwork_name,
                "paths": template_paths,
                "guide_image": guide_image,
                "total_paths": len(template_paths),
                "canvas_size": {"width": self.canvas_width, "height": self.canvas_height}
            }

        except Exception as e:
            logger.error(f"创建练习模板失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_artwork_template(self, artwork_name: str, difficulty: str = "medium") -> List[Dict[str, Any]]:
        """获取名画的简化线条模板"""
        templates = {
            "蒙娜丽莎": {
                "easy": [
                    {
                        "id": "simple_line",
                        "points": [
                            {"x": 100, "y": 200}, {"x": 500, "y": 200}
                        ],
                        "color": "#cccccc",
                        "size": 4,
                        "order": 0
                    }
                ],
                "medium": [
                    {
                        "id": "simple_circle",
                        "points": [
                            {"x": 400, "y": 300}, {"x": 500, "y": 200},
                            {"x": 600, "y": 300}, {"x": 500, "y": 400},
                            {"x": 400, "y": 400}, {"x": 300, "y": 300},
                            {"x": 300, "y": 200}, {"x": 400, "y": 200}
                        ],
                        "color": "#cccccc",
                        "size": 4,
                        "order": 0
                    },
                    {
                        "id": "simple_line",
                        "points": [
                            {"x": 150, "y": 100}, {"x": 450, "y": 100}
                        ],
                        "color": "#cccccc",
                        "size": 3,
                        "order": 1
                    }
                ],
                "hard": [
                    {
                        "id": "face_outline",
                        "points": [
                            {"x": 250, "y": 80}, {"x": 300, "y": 70}, {"x": 350, "y": 80},
                            {"x": 380, "y": 120}, {"x": 390, "y": 160}, {"x": 380, "y": 200},
                            {"x": 350, "y": 240}, {"x": 300, "y": 250}, {"x": 250, "y": 240},
                            {"x": 220, "y": 200}, {"x": 210, "y": 160}, {"x": 220, "y": 120},
                            {"x": 250, "y": 80}
                        ],
                        "color": "#cccccc",
                        "size": 2,
                        "order": 0
                    },
                    {
                        "id": "left_eye",
                        "points": [
                            {"x": 270, "y": 140}, {"x": 290, "y": 135}, {"x": 310, "y": 140}
                        ],
                        "color": "#cccccc",
                        "size": 2,
                        "order": 1
                    },
                    {
                        "id": "right_eye",
                        "points": [
                            {"x": 330, "y": 140}, {"x": 350, "y": 135}, {"x": 370, "y": 140}
                        ],
                        "color": "#cccccc",
                        "size": 2,
                        "order": 2
                    },
                    {
                        "id": "mouth",
                        "points": [
                            {"x": 280, "y": 200}, {"x": 300, "y": 205}, {"x": 320, "y": 200}
                        ],
                        "color": "#cccccc",
                        "size": 2,
                        "order": 3
                    }
                ]
            },
            "星夜": [
                {
                    "id": "swirl_1",
                    "points": [
                        {"x": 200, "y": 150}, {"x": 250, "y": 120}, {"x": 300, "y": 130},
                        {"x": 350, "y": 160}, {"x": 380, "y": 200}, {"x": 370, "y": 250},
                        {"x": 330, "y": 280}, {"x": 280, "y": 270}, {"x": 240, "y": 240},
                        {"x": 220, "y": 200}, {"x": 200, "y": 150}
                    ],
                    "color": "#cccccc",
                    "size": 2,
                    "order": 0
                }
            ]
        }

        artwork_templates = templates.get(artwork_name, {})
        if isinstance(artwork_templates, dict):
            return artwork_templates.get(difficulty, [])
        else:
            # 兼容旧格式
            return artwork_templates if difficulty == "medium" else []

    def _create_sample_template(self, difficulty: str = "medium") -> Dict[str, Any]:
        """
        从sample.png创建练习模板

        Args:
            difficulty: 难度级别

        Returns:
            包含抽取线条的模板
        """
        try:
            # 查找Lotus fg.jpg文件用于轨迹抽取
            sample_path = None
            possible_paths = [
                "arts/Lotus/fg.jpg",
                "../arts/Lotus/fg.jpg",
                "MemoryBrush/frontend/public/Lotus/fg.jpg",
                "/app/arts/Lotus/fg.jpg",
                "../../arts/Lotus/fg.jpg"
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    sample_path = path
                    break

            if not sample_path:
                logger.error("未找到Lotus fg.jpg文件")
                return {
                    "success": False,
                    "error": "未找到Lotus fg.jpg文件"
                }

            logger.info(f"使用Lotus fg.jpg文件: {sample_path}")

            # 使用图像线条抽取器
            extraction_result = image_line_extractor.extract_lines_from_image(sample_path)

            if not extraction_result["success"]:
                logger.error(f"线条抽取失败: {extraction_result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": f"线条抽取失败: {extraction_result.get('error', '未知错误')}"
                }

            # 保留所有检测到的线条，不再根据难度限制
            guide_paths = extraction_result["guide_paths"]
            logger.info(f"检测到 {len(guide_paths)} 条线条，全部保留用于描线练习")

            # 优化线条用于描线练习
            optimized_paths = image_line_extractor.optimize_for_tracing(guide_paths)

            # 生成引导图像（使用原图的base64编码）
            guide_image = self._create_sample_guide_image(sample_path)

            return {
                "success": True,
                "artwork_name": "sample",
                "paths": optimized_paths,
                "guide_image": guide_image,
                "total_paths": len(optimized_paths),
                "canvas_size": {"width": self.canvas_width, "height": self.canvas_height},
                "extracted_lines": len(optimized_paths)
            }

        except Exception as e:
            logger.error(f"创建sample模板失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_sample_guide_image(self, image_path: str) -> Optional[str]:
        """
        创建图像的base64编码

        Args:
            image_path: 图像文件路径

        Returns:
            base64编码的图像字符串
        """
        try:
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')

                # 根据文件扩展名确定MIME类型
                if image_path.lower().endswith('.jpg') or image_path.lower().endswith('.jpeg'):
                    mime_type = "image/jpeg"
                elif image_path.lower().endswith('.png'):
                    mime_type = "image/png"
                else:
                    mime_type = "image/jpeg"  # 默认使用jpeg

                return f"data:{mime_type};base64,{base64_string}"
        except Exception as e:
            logger.error(f"创建引导图像失败: {str(e)}")
            return None


# 全局服务实例
line_extraction_service = LineExtractionService()
