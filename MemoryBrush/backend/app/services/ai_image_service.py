"""
AI图像生成服务
使用SiliconFlow API生成基于用户线条的艺术作品
"""

import aiohttp
import base64
import io
import json
from typing import Dict, Any, Optional, List
from PIL import Image, ImageDraw

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("ai_image_service")


class AIImageService:
    """AI图像生成服务"""

    def __init__(self):
        self.api_key = settings.SILICONFLOW_API_KEY
        self.base_url = settings.SILICONFLOW_BASE_URL
        self.model = settings.AI_IMAGE_MODEL
        self.default_size = settings.AI_IMAGE_SIZE
        self.default_steps = settings.AI_IMAGE_STEPS
        self.default_guidance_scale = settings.AI_IMAGE_GUIDANCE_SCALE

    async def generate_artwork_from_lines(
        self,
        canvas_data: str,
        paths: List[Dict[str, Any]],
        style: str,
        matched_artwork: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        基于用户线条生成艺术作品

        Args:
            canvas_data: 原始画布数据 (base64)
            paths: 用户绘制的路径数据
            style: 艺术风格
            matched_artwork: 匹配的名画信息

        Returns:
            生成的图像信息
        """
        try:
            logger.info(f"开始生成艺术作品，风格: {style}")

            # 1. 处理用户线条，创建线条图像
            line_image = self._create_line_image_from_paths(paths)

            # 2. 构建提示词
            prompt, negative_prompt = self._build_prompt(style, matched_artwork, paths)

            # 3. 调用SiliconFlow API生成图像
            generated_image_url = await self._call_siliconflow_api(
                prompt=prompt,
                negative_prompt=negative_prompt,
                init_image=line_image,
                strength=0.3  # 强烈保留原始线条结构
            )

            # 4. 直接返回AI生成的图片URL，不下载到本地
            return {
                "success": True,
                "generated_image_url": generated_image_url,  # 直接使用AI返回的URL
                "original_canvas": canvas_data,
                "style": style,
                "prompt": prompt,
                "matched_artwork": matched_artwork
            }

        except Exception as e:
            logger.error(f"AI生图失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "original_canvas": canvas_data
            }

    def _create_line_image_from_paths(self, paths: List[Dict[str, Any]]) -> str:
        """从路径数据创建线条图像"""
        try:
            # 创建白色背景的图像 - 使用768x1024分辨率匹配AI生成图
            width, height = 768, 1024
            image = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(image)

            # 绘制用户的线条 - 使用更粗的线条和黑色确保可见性
            for path in paths:
                points = path.get("points", [])
                color = "#000000"  # 强制使用黑色，确保线条清晰可见
                size = max(path.get("size", 5), 8)  # 最小线宽8px，确保AI能识别

                if len(points) < 2:
                    continue

                # 将点坐标转换为PIL格式
                pil_points = []
                for point in points:
                    x = int(point.get("x", 0) * width / 768)  # 原画布是768px
                    y = int(point.get("y", 0) * height / 1024)  # 原画布是1024px
                    pil_points.append((x, y))

                # 绘制线条 - 使用更粗的线条
                if len(pil_points) >= 2:
                    for i in range(len(pil_points) - 1):
                        draw.line([pil_points[i], pil_points[i + 1]], fill=color, width=size)

                # 在线条端点绘制小圆点，增强线条的连续性
                for point in pil_points:
                    draw.ellipse([point[0] - size // 2, point[1] - size // 2,
                                  point[0] + size // 2, point[1] + size // 2], fill=color)

            # 转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{image_base64}"

        except Exception as e:
            logger.error(f"创建线条图像失败: {str(e)}")
            # 返回空白图像
            image = Image.new('RGB', (1024, 1024), 'white')
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{image_base64}"

    def _build_prompt(self, style: str, matched_artwork: Dict[str, Any],
                      paths: List[Dict[str, Any]]) -> tuple[str, str]:
        """构建AI生图的提示词和负面提示词"""
        # 简洁的正面提示词 - 专注于核心要求
        prompt = "preserve original black lines, fine art style, elegant composition"

        # 根据线条特征选择艺术风格
        if paths:
            has_curves = any(len(path.get("points", [])) > 5 for path in paths)
            line_count = len(paths)

            if has_curves:
                prompt += ", flowing watercolor style"
            else:
                prompt += ", geometric abstract style"

            if line_count > 5:
                prompt += ", detailed artwork"
            else:
                prompt += ", minimalist art"

        # 简单的艺术媒介选择
        art_styles = [
            "oil painting",
            "watercolor",
            "ink painting",
            "abstract art",
            "contemporary art"
        ]

        import random
        selected_style = random.choice(art_styles)
        prompt += f", {selected_style}, museum quality"

        # 负面提示词 - 明确禁止的元素
        negative_prompt = "cartoon, anime, manga, comic, characters, people, faces, "
        negative_prompt += "modify lines, remove lines, change lines, distort lines, "
        negative_prompt += "low quality, blurry, pixelated, ugly, deformed"

        return prompt, negative_prompt

    async def _call_siliconflow_api(
        self,
        prompt: str,
        negative_prompt: Optional[str] = None,
        init_image: Optional[str] = None,
        strength: float = 0.3
    ) -> str:
        """调用SiliconFlow API生成图像"""
        if not self.api_key:
            raise ValueError("SiliconFlow API密钥未配置")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 构建请求数据 - 根据SiliconFlow API文档格式
        data = {
            "model": self.model,
            "prompt": prompt,
            "image_size": self.default_size,
            "batch_size": 1,
            "num_inference_steps": self.default_steps,
            "guidance_scale": self.default_guidance_scale
        }

        # 添加负面提示词
        if negative_prompt:
            data["negative_prompt"] = negative_prompt

        # 如果有初始图像，添加到请求中
        if init_image:
            # 确保图像数据包含完整的data URL格式
            if not init_image.startswith("data:image"):
                init_image = f"data:image/png;base64,{init_image}"

            data["image"] = init_image
            # 使用传入的strength参数
            data["strength"] = strength

        # SiliconFlow只有一个生成端点
        endpoint = f"{self.base_url}/images/generations"

        # 记录请求详情
        logger.info(f"调用SiliconFlow API: {endpoint}")
        logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")

        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, headers=headers, json=data) as response:
                response_text = await response.text()
                logger.info(f"API响应状态: {response.status}")
                logger.info(f"API响应内容: {response_text}")

                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        # 根据SiliconFlow文档，检查不同的响应格式
                        if result.get("images") and len(result["images"]) > 0:
                            # 如果返回的是images数组
                            return result["images"][0]["url"]
                        elif result.get("data") and len(result["data"]) > 0:
                            # 如果返回的是data数组
                            return result["data"][0]["url"]
                        elif result.get("url"):
                            # 如果直接返回url
                            return result["url"]
                        else:
                            logger.error(f"未知的API响应格式: {result}")
                            raise ValueError("API返回数据格式错误")
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}")
                        raise ValueError("API响应不是有效的JSON格式")
                else:
                    logger.error(f"SiliconFlow API错误: {response.status} - {response_text}")
                    raise ValueError(f"API调用失败: {response.status}")


# 全局服务实例
ai_image_service = AIImageService()
