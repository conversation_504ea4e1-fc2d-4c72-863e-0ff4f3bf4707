"""
图像线条抽取服务
从图片中自动抽取线条路径，用于描线练习
"""

import cv2
import numpy as np
from PIL import Image
from typing import List, Dict, Any, Tuple
import os
from pathlib import Path


class ImageLineExtractor:
    """图像线条抽取器"""

    def __init__(self):
        self.min_line_length = 20  # 降低最小线条长度，检测更多线条
        self.max_line_gap = 15     # 增加最大线条间隙，连接更多断开的线
        self.canny_low = 30        # 降低Canny低阈值，检测更多边缘
        self.canny_high = 100      # 降低Canny高阈值，检测更多边缘
        self.hough_threshold = 50  # 降低霍夫变换阈值，检测更多线条

    def extract_lines_from_image(self, image_path: str) -> Dict[str, Any]:
        """
        从图片中抽取线条

        Args:
            image_path: 图片文件路径

        Returns:
            包含线条路径和原图信息的字典
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")

            # 获取图片尺寸
            height, width = image.shape[:2]

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Canny边缘检测
            edges = cv2.Canny(blurred, self.canny_low, self.canny_high)

            # 霍夫线变换检测直线
            lines = cv2.HoughLinesP(
                edges,
                rho=1,
                theta=np.pi / 180,
                threshold=self.hough_threshold,
                minLineLength=self.min_line_length,
                maxLineGap=self.max_line_gap
            )

            # 转换线条为路径格式
            guide_paths = []
            if lines is not None:
                for i, line in enumerate(lines):
                    x1, y1, x2, y2 = line[0]

                    # 创建路径点
                    points = [
                        {"x": int(x1), "y": int(y1)},
                        {"x": int(x2), "y": int(y2)}
                    ]

                    guide_paths.append({
                        "id": f"extracted_line_{i}",
                        "points": points,
                        "color": "#cccccc",
                        "size": 3,
                        "order": i
                    })

            # 总是使用轮廓检测来补充线条检测，确保检测到所有几何图形
            contour_paths = self._extract_contours(edges, width, height)
            guide_paths.extend(contour_paths)

            # 去重和排序
            guide_paths = self._deduplicate_and_sort_paths(guide_paths)

            # 增加路径数量限制，确保能提取所有主要线条
            guide_paths = guide_paths[:20]  # 增加到20条线

            return {
                "success": True,
                "guide_paths": guide_paths,
                "image_width": width,
                "image_height": height,
                "total_lines": len(guide_paths)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "guide_paths": [],
                "image_width": 800,
                "image_height": 600,
                "total_lines": 0
            }

    def _extract_contours(self, edges: np.ndarray, width: int, height: int) -> List[Dict[str, Any]]:
        """
        从边缘图像中提取轮廓作为线条

        Args:
            edges: 边缘检测后的图像
            width: 图像宽度
            height: 图像高度

        Returns:
            轮廓路径列表
        """
        contour_paths = []

        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for i, contour in enumerate(contours):
            # 降低轮廓面积阈值，检测更多小的几何图形
            if cv2.contourArea(contour) < 50:
                continue

            # 简化轮廓
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 转换为点列表
            points = []
            for point in approx:
                try:
                    # OpenCV轮廓点的格式通常是 [[x, y]]
                    pt = point.flatten()
                    if len(pt) >= 2:
                        x, y = int(pt[0]), int(pt[1])
                        points.append({"x": x, "y": y})
                except (IndexError, TypeError, AttributeError):
                    continue

            # 至少需要2个点
            if len(points) >= 2:
                contour_paths.append({
                    "id": f"contour_line_{i}",
                    "points": points,
                    "color": "#cccccc",
                    "size": 2,
                    "order": i + 100  # 给轮廓更高的order值
                })

        return contour_paths

    def optimize_for_tracing(self, guide_paths: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        优化线条路径，使其更适合描线练习

        Args:
            guide_paths: 原始线条路径

        Returns:
            优化后的线条路径
        """
        optimized_paths = []

        for path in guide_paths:
            points = path["points"]

            # 如果是直线，保持原样
            if len(points) == 2:
                optimized_paths.append(path)
                continue

            # 对于复杂路径，进行平滑处理
            if len(points) > 2:
                # 简化路径点
                simplified_points = self._simplify_path(points)

                if len(simplified_points) >= 2:
                    optimized_path = path.copy()
                    optimized_path["points"] = simplified_points
                    optimized_paths.append(optimized_path)

        return optimized_paths

    def _simplify_path(self, points: List[Dict[str, int]], tolerance: float = 5.0) -> List[Dict[str, int]]:
        """
        使用Douglas-Peucker算法简化路径

        Args:
            points: 原始点列表
            tolerance: 简化容差

        Returns:
            简化后的点列表
        """
        if len(points) <= 2:
            return points

        # 简单的距离过滤
        simplified = [points[0]]  # 保留起点

        for i in range(1, len(points) - 1):
            # 计算当前点到前一个保留点的距离
            prev_point = simplified[-1]
            curr_point = points[i]

            distance = np.sqrt(
                (curr_point["x"] - prev_point["x"]) ** 2 +
                (curr_point["y"] - prev_point["y"]) ** 2
            )

            # 如果距离足够大，保留这个点
            if distance > tolerance:
                simplified.append(curr_point)

        simplified.append(points[-1])  # 保留终点

        return simplified

    def _deduplicate_and_sort_paths(self, guide_paths: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重和排序路径

        Args:
            guide_paths: 原始路径列表

        Returns:
            去重排序后的路径列表
        """
        # 简单的去重逻辑：移除过于相似的路径
        unique_paths = []

        for path in guide_paths:
            is_duplicate = False
            for existing_path in unique_paths:
                if self._paths_are_similar(path, existing_path):
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_paths.append(path)

        # 按路径长度排序，优先显示较长的主要线条
        unique_paths.sort(key=lambda p: len(p["points"]), reverse=True)

        # 重新分配order
        for i, path in enumerate(unique_paths):
            path["order"] = i
            path["id"] = f"line_{i}"

        return unique_paths

    def _paths_are_similar(self, path1: Dict[str, Any], path2: Dict[str, Any], threshold: float = 30.0) -> bool:
        """
        判断两条路径是否相似

        Args:
            path1: 第一条路径
            path2: 第二条路径
            threshold: 相似度阈值

        Returns:
            是否相似
        """
        points1 = path1["points"]
        points2 = path2["points"]

        # 如果点数差异很大，认为不相似
        if abs(len(points1) - len(points2)) > 2:
            return False

        # 计算起点和终点的距离
        if len(points1) >= 2 and len(points2) >= 2:
            start_dist = np.sqrt(
                (points1[0]["x"] - points2[0]["x"]) ** 2 +
                (points1[0]["y"] - points2[0]["y"]) ** 2
            )
            end_dist = np.sqrt(
                (points1[-1]["x"] - points2[-1]["x"]) ** 2 +
                (points1[-1]["y"] - points2[-1]["y"]) ** 2
            )

            # 如果起点和终点都很接近，认为是相似路径
            return start_dist < threshold and end_dist < threshold

        return False


# 全局实例
image_line_extractor = ImageLineExtractor()
