"""
艺术作品模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Artwork(Base):
    """艺术作品模型"""
    __tablename__ = "artworks"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    session_id = Column(String(100), nullable=False, index=True)
    
    # 作品信息
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(500), nullable=False)
    thumbnail_url = Column(String(500), nullable=True)
    
    # 游戏相关
    level = Column(Integer, nullable=False)
    stage = Column(Integer, nullable=False)
    
    # 元数据
    canvas_width = Column(Integer, nullable=True)
    canvas_height = Column(Integer, nullable=True)
    stroke_count = Column(Integer, default=0)
    colors_used = Column(Text, nullable=True)  # JSON字符串存储颜色数组
    drawing_time = Column(Float, default=0.0)  # 绘画时间（秒）
    brush_types = Column(Text, nullable=True)  # JSON字符串存储画笔类型数组
    complexity = Column(Float, default=0.0)  # 复杂度评分
    
    # 社交功能
    is_public = Column(Boolean, default=False)
    likes = Column(Integer, default=0)
    tags = Column(Text, nullable=True)  # JSON字符串存储标签数组
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    user = relationship("User", backref="artworks")

    def __repr__(self):
        return f"<Artwork(id={self.id}, title='{self.title}', user_id={self.user_id})>"
