"""
认知评估模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Assessment(Base):
    """认知评估模型"""
    __tablename__ = "assessments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    session_id = Column(String(100), ForeignKey("game_sessions.session_id"), nullable=False)

    # 评估类型
    assessment_type = Column(String(50), nullable=False)  # 'cognitive', 'motor', 'visual', 'memory'
    assessment_name = Column(String(100), nullable=False)

    # 评估结果
    score = Column(Float, nullable=False)
    max_score = Column(Float, nullable=False)
    percentage = Column(Float, nullable=False)

    # 详细指标
    reaction_time = Column(Float, nullable=True)  # 反应时间（毫秒）
    accuracy = Column(Float, nullable=True)  # 准确率
    consistency = Column(Float, nullable=True)  # 一致性
    improvement = Column(Float, nullable=True)  # 相比上次的改进

    # 认知指标
    attention_score = Column(Float, nullable=True)
    memory_score = Column(Float, nullable=True)
    executive_function_score = Column(Float, nullable=True)
    visuospatial_score = Column(Float, nullable=True)
    language_score = Column(Float, nullable=True)

    # 运动技能指标
    fine_motor_score = Column(Float, nullable=True)
    hand_eye_coordination = Column(Float, nullable=True)
    tremor_analysis = Column(Float, nullable=True)

    # 评估数据
    raw_data = Column(Text, nullable=True)  # JSON字符串存储原始数据
    analysis_data = Column(Text, nullable=True)  # JSON字符串存储分析数据
    recommendations = Column(Text, nullable=True)  # JSON字符串存储建议

    # 状态
    is_baseline = Column(Boolean, default=False)  # 是否为基线评估
    is_valid = Column(Boolean, default=True)  # 评估是否有效

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    user = relationship("User", backref="assessments")
    game_session = relationship("GameSession", backref="assessments")

    def __repr__(self):
        return f"<Assessment(id={self.id}, type='{self.assessment_type}', score={self.score})>"
