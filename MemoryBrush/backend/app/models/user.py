"""
用户模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from app.core.database import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=True)
    avatar = Column(String(255), nullable=True)
    age = Column(Integer, nullable=True)

    # 用户档案信息
    display_name = Column(String(100), nullable=True)
    medical_condition = Column(String(50), nullable=True)  # 'alzheimer', 'parkinson', 'healthy', 'other'
    preferred_difficulty = Column(String(20), default='easy')  # 'easy', 'medium', 'hard'
    accessibility_needs = Column(Text, nullable=True)  # JSON字符串存储数组
    emergency_contact = Column(String(100), nullable=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"
