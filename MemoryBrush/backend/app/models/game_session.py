"""
游戏会话模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class GameSession(Base):
    """游戏会话模型"""
    __tablename__ = "game_sessions"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # 游戏信息
    level = Column(Integer, nullable=False)
    stage = Column(Integer, nullable=False)

    # 时间信息
    start_time = Column(DateTime(timezone=True), server_default=func.now())
    end_time = Column(DateTime(timezone=True), nullable=True)
    time_spent = Column(Float, default=0.0)  # 花费时间（秒）

    # 状态和分数
    status = Column(String(20), default='active')  # 'active', 'completed', 'paused', 'abandoned'
    current_score = Column(Integer, default=0)
    final_score = Column(Integer, nullable=True)

    # 游戏数据
    game_data = Column(Text, nullable=True)  # JSON字符串存储游戏数据
    progress_data = Column(Text, nullable=True)  # JSON字符串存储进度数据

    # 完成状态
    is_completed = Column(Boolean, default=False)
    completion_percentage = Column(Float, default=0.0)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    user = relationship("User", backref="game_sessions")

    def __repr__(self):
        return f"<GameSession(id={self.id}, session_id='{self.session_id}', level={self.level}, stage={self.stage})>"
