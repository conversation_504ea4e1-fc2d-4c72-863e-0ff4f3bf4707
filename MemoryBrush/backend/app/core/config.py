"""
应用配置模块
"""

import os
from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用设置"""

    # 基础配置
    APP_NAME: str = "记忆画笔 API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=True, env="DEBUG")

    # 服务器配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")

    # 跨域配置
    ALLOWED_HOSTS: List[str] = Field(
        default=[
            "http://localhost:5173",
            "http://127.0.0.1:5173",
            "http://localhost:5174",
            "http://127.0.0.1:5174"
        ],
        env="ALLOWED_HOSTS"
    )

    # 数据库配置
    DATABASE_URL: str = Field(
        default="sqlite:///./memory_brush.db",
        env="DATABASE_URL"
    )

    # 安全配置
    SECRET_KEY: str = Field(
        default="memory-brush-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 文件上传配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    UPLOAD_DIR: str = "uploads"
    STATIC_DIR: str = "static"
    DATA_DIR: str = "data"  # 数据存储目录

    # AI模型配置
    MODEL_DIR: str = "models"
    ENABLE_AI_FEATURES: bool = Field(default=True, env="ENABLE_AI_FEATURES")

    # SiliconFlow AI生图配置
    SILICONFLOW_API_KEY: str = Field(
        default="sk-ngcptqxipgoakbavgualbabodbrndlbgwpdaidphulejxaod", env="SILICONFLOW_API_KEY")
    SILICONFLOW_BASE_URL: str = Field(default="https://api.siliconflow.cn/v1", env="SILICONFLOW_BASE_URL")
    AI_IMAGE_MODEL: str = Field(default="Kwai-Kolors/Kolors", env="AI_IMAGE_MODEL")
    AI_IMAGE_SIZE: str = Field(default="768x1024", env="AI_IMAGE_SIZE")
    AI_IMAGE_STEPS: int = Field(default=20, env="AI_IMAGE_STEPS")
    AI_IMAGE_GUIDANCE_SCALE: float = Field(default=7.5, env="AI_IMAGE_GUIDANCE_SCALE")

    # 游戏配置
    MAX_CANVAS_SIZE: int = 2048  # 最大画布尺寸
    DEFAULT_BRUSH_SIZE: int = 5
    MAX_BRUSH_SIZE: int = 50

    # 认知评估配置
    ASSESSMENT_LEVELS: int = 4
    MIN_DRAWING_TIME: int = 5  # 最小绘画时间（秒）
    MAX_DRAWING_TIME: int = 300  # 最大绘画时间（秒）

    # 描线练习难度配置
    TRACE_DIFFICULTY: str = Field(default="easy", env="TRACE_DIFFICULTY")  # easy, medium, hard

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = "logs/memory_brush.log"

    # Redis配置（可选）
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    ENABLE_REDIS: bool = Field(default=False, env="ENABLE_REDIS")

    class Config:
        env_file = ".env"
        case_sensitive = True

    def get_trace_difficulty_config(self):
        """获取当前描线练习难度配置"""
        difficulty_configs = {
            "easy": {
                "name": "简单",
                "accuracyThreshold": 0.4,
                "tolerance": 50,
                "description": "40%准确度，50像素容差",
                "animationSpeed": 1.0  # 动画速度倍数，越大越快
            },
            "medium": {
                "name": "中等",
                "accuracyThreshold": 0.55,
                "tolerance": 35,
                "description": "55%准确度，35像素容差",
                "animationSpeed": 1.2
            },
            "hard": {
                "name": "困难",
                "accuracyThreshold": 0.7,
                "tolerance": 25,
                "description": "70%准确度，25像素容差",
                "animationSpeed": 1.5  # 困难模式速度较慢，让用户仔细观察
            }
        }
        return difficulty_configs.get(self.TRACE_DIFFICULTY, difficulty_configs["medium"])


# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs("logs", exist_ok=True)
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.STATIC_DIR, exist_ok=True)
os.makedirs(settings.MODEL_DIR, exist_ok=True)
os.makedirs(settings.DATA_DIR, exist_ok=True)
