"""
数据库配置和连接管理
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import asyncio
from typing import AsyncGenerator

from .config import settings

# 创建数据库引擎
if settings.DATABASE_URL.startswith("sqlite"):
    # SQLite配置
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={
            "check_same_thread": False,
            "timeout": 20
        },
        poolclass=StaticPool,
        echo=settings.DEBUG
    )
else:
    # PostgreSQL或其他数据库配置
    engine = create_engine(
        settings.DATABASE_URL,
        echo=settings.DEBUG
    )

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """初始化数据库"""
    # 导入所有模型以确保它们被注册
    from app.models.user import User
    from app.models.artwork import Artwork
    from app.models.game_session import GameSession
    from app.models.assessment import Assessment

    # 创建所有表
    Base.metadata.create_all(bind=engine)

    # 创建默认数据
    await create_default_data()


async def create_default_data():
    """创建默认数据"""
    db = SessionLocal()
    try:
        # 这里可以添加默认用户、游戏关卡等数据
        pass
    finally:
        db.close()


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal

    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()

    def close_session(self, session):
        """关闭数据库会话"""
        session.close()

    async def execute_query(self, query: str, params: dict = None):
        """执行查询"""
        with self.get_session() as session:
            result = session.execute(query, params or {})
            session.commit()
            return result

    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
                return True
        except Exception:
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()
