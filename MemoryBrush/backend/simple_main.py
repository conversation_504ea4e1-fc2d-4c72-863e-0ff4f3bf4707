"""
记忆画笔 (Memory Brush) - 简化版后端主入口
只使用核心依赖，确保能够快速启动

主题: 记忆画笔 (Memory Brush)
口号: 画笔在手，奇迹我有！/ My Brush, My Magic!
团队: 奇迹创造者 (Magic Makers)
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os

# 创建FastAPI应用
app = FastAPI(
    title="记忆画笔 API (简化版)",
    description="""
    ## 记忆画笔 (Memory Brush) - 认知艺术疗法游戏

    **主题**: 记忆画笔 (Memory Brush)  
    **口号**: 画笔在手，奇迹我有！/ My Brush, My Magic!  
    **团队**: 奇迹创造者 (Magic Makers)

    ### 功能特性
    - 🎨 多级别绘画游戏
    - 🧠 认知功能评估
    - 📊 进度跟踪分析
    - 🎯 个性化推荐
    - 🏆 成就系统

    ### 目标用户
    专为老年人群，特别是阿尔茨海默症和帕金森疾病患者设计
    """,
    version="1.0.0",
    contact={
        "name": "奇迹创造者团队",
        "email": "<EMAIL>",
    },
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径 - 服务状态检查"""
    return {
        "message": "记忆画笔 API 服务运行中",
        "slogan": "画笔在手，奇迹我有！",
        "english_slogan": "My Brush, My Magic!",
        "team": "奇迹创造者 (Magic Makers)",
        "version": "1.0.0",
        "status": "healthy",
        "features": [
            "多级别绘画游戏",
            "认知功能评估", 
            "进度跟踪分析",
            "个性化推荐",
            "成就系统"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "Memory Brush API",
        "message": "服务运行正常"
    }

# 游戏级别API
@app.get("/api/v1/games/levels")
async def get_game_levels():
    """获取游戏级别"""
    levels = [
        {
            "level": 1,
            "name": "简单线条",
            "description": "学习基础的线条绘制",
            "stages": ["自由画线", "描摹练习", "几何图形绘制"],
            "difficulty": "简单",
            "estimated_time": 15
        },
        {
            "level": 2,
            "name": "二维转三维",
            "description": "将平面图形转换为立体图形",
            "stages": ["基础立体图形", "色彩填充", "质感绘制"],
            "difficulty": "中等",
            "estimated_time": 25
        },
        {
            "level": 3,
            "name": "引导创作",
            "description": "在引导下完成完整画面",
            "stages": ["抽象艺术", "几何静物", "生活物品"],
            "difficulty": "中等",
            "estimated_time": 35
        },
        {
            "level": 4,
            "name": "照片艺术化",
            "description": "将照片转换为艺术作品",
            "stages": ["轮廓提取", "风格选择", "智能渲染"],
            "difficulty": "困难",
            "estimated_time": 45
        }
    ]
    return {
        "success": True,
        "data": levels
    }

@app.post("/api/v1/games/sessions/start")
async def start_game_session(level: int = 1, stage: int = 1):
    """开始游戏会话"""
    if level < 1 or level > 4:
        raise HTTPException(status_code=400, detail="无效的游戏级别")
    
    session_data = {
        "session_id": f"session_{level}_{stage}",
        "level": level,
        "stage": stage,
        "start_time": "2024-01-01T00:00:00Z",
        "status": "active"
    }
    
    return {
        "success": True,
        "message": "游戏会话已开始",
        "data": session_data
    }

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    print(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "status_code": 500
        }
    )

if __name__ == "__main__":
    print("🎨 记忆画笔服务启动中...")
    print("🖌️ 画笔在手，奇迹我有！")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
