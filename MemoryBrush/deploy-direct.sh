#!/bin/bash

# MemoryBrush 直接部署脚本

set -e

echo "🚀 开始直接部署 MemoryBrush..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "${BLUE}📍 检测到服务器IP: ${SERVER_IP}${NC}"

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}❌ 请不要使用root用户运行此脚本${NC}"
   exit 1
fi

# 检查必要的命令
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 未安装，请先安装${NC}"
        return 1
    fi
}

echo -e "${YELLOW}🔍 检查环境依赖...${NC}"

# 检查Node.js
if ! check_command node; then
    echo -e "${YELLOW}📦 安装 Node.js...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# 检查Python3
if ! check_command python3; then
    echo -e "${YELLOW}📦 安装 Python3...${NC}"
    sudo apt update
    sudo apt install -y python3 python3-pip python3-venv
fi

# 检查Nginx
if ! check_command nginx; then
    echo -e "${YELLOW}📦 安装 Nginx...${NC}"
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
fi

# 安装系统依赖
echo -e "${YELLOW}📦 安装系统依赖...${NC}"
sudo apt install -y build-essential libopencv-dev python3-opencv curl

# 创建项目目录
PROJECT_DIR="/var/www/memory-brush"
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${YELLOW}📁 创建项目目录...${NC}"
    sudo mkdir -p $PROJECT_DIR
    sudo chown $USER:$USER $PROJECT_DIR
    
    # 复制当前目录的文件到项目目录
    cp -r . $PROJECT_DIR/
fi

cd $PROJECT_DIR

# 部署后端
echo -e "${YELLOW}🔧 部署后端...${NC}"
cd $PROJECT_DIR/backend

# 创建虚拟环境
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 创建环境配置文件
cat > .env << EOF
DEBUG=False
SECRET_KEY=$(openssl rand -hex 32)
DATABASE_URL=sqlite:///./memory_brush.db
ALLOWED_HOSTS=http://${SERVER_IP},http://localhost
EOF

# 创建systemd服务
sudo tee /etc/systemd/system/memory-brush-backend.service > /dev/null << EOF
[Unit]
Description=Memory Brush Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR/backend
Environment=PATH=$PROJECT_DIR/backend/venv/bin
ExecStart=$PROJECT_DIR/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启动后端服务
sudo systemctl daemon-reload
sudo systemctl start memory-brush-backend
sudo systemctl enable memory-brush-backend

# 部署前端
echo -e "${YELLOW}🎨 部署前端...${NC}"
cd $PROJECT_DIR/frontend

# 创建生产环境配置
cat > .env.production << EOF
VITE_API_URL=http://${SERVER_IP}:8000
VITE_APP_TITLE=记忆画笔
VITE_APP_VERSION=1.0.0
EOF

# 安装依赖
npm install

# 构建生产版本
npm run build

# 配置Nginx
sudo tee /etc/nginx/sites-available/memory-brush > /dev/null << EOF
server {
    listen 80;
    server_name ${SERVER_IP} _;
    
    root $PROJECT_DIR/frontend/dist;
    index index.html;
    
    # 处理SPA路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/memory-brush /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
if sudo nginx -t; then
    sudo systemctl restart nginx
else
    echo -e "${RED}❌ Nginx配置错误${NC}"
    exit 1
fi

# 设置文件权限
sudo chown -R $USER:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR
sudo chmod -R 775 $PROJECT_DIR/backend/uploads 2>/dev/null || true

# 配置防火墙
echo -e "${YELLOW}🔒 配置防火墙...${NC}"
if command -v ufw &> /dev/null; then
    sudo ufw allow 80 2>/dev/null || true
    sudo ufw allow 8000 2>/dev/null || true
fi

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 5

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"

if systemctl is-active --quiet memory-brush-backend; then
    echo -e "${GREEN}✅ 后端服务正常${NC}"
else
    echo -e "${RED}❌ 后端服务异常${NC}"
    echo -e "${YELLOW}查看日志: sudo journalctl -u memory-brush-backend -n 20${NC}"
fi

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx服务正常${NC}"
else
    echo -e "${RED}❌ Nginx服务异常${NC}"
    echo -e "${YELLOW}查看日志: sudo journalctl -u nginx -n 20${NC}"
fi

# 测试访问
echo -e "${YELLOW}🧪 测试服务访问...${NC}"

if curl -f http://localhost:80 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 前端访问正常${NC}"
else
    echo -e "${RED}❌ 前端访问异常${NC}"
fi

if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端API正常${NC}"
else
    echo -e "${RED}❌ 后端API异常，尝试基础检查...${NC}"
    if curl -f http://localhost:8000/ > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  后端运行但health端点可能不存在${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${GREEN}📱 前端访问地址: http://${SERVER_IP}${NC}"
echo -e "${GREEN}🔧 后端API地址: http://${SERVER_IP}:8000${NC}"
echo ""
echo -e "${BLUE}📋 常用命令：${NC}"
echo "   查看后端日志: sudo journalctl -u memory-brush-backend -f"
echo "   查看Nginx日志: sudo tail -f /var/log/nginx/access.log"
echo "   重启后端: sudo systemctl restart memory-brush-backend"
echo "   重启前端: sudo systemctl restart nginx"
echo "   查看服务状态: sudo systemctl status memory-brush-backend nginx"
echo ""
echo -e "${YELLOW}💡 如果遇到问题，请查看 DIRECT_DEPLOY.md 获取详细故障排除指南${NC}"
