#!/bin/bash

# MemoryBrush 权限修复脚本

set -e

echo "🔐 修复 MemoryBrush 文件权限..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

PROJECT_DIR="/var/www/memory-brush"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}❌ 项目目录不存在: $PROJECT_DIR${NC}"
    exit 1
fi

# 检查caddy用户是否存在
if ! id "caddy" &>/dev/null; then
    echo -e "${RED}❌ caddy用户不存在，请先安装Caddy${NC}"
    exit 1
fi

echo -e "${YELLOW}📁 设置项目目录权限...${NC}"

# 设置项目根目录权限
sudo chown -R $USER:caddy $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR

echo -e "${YELLOW}🎨 设置前端文件权限...${NC}"

# 确保前端dist目录存在并设置权限
if [ -d "$PROJECT_DIR/frontend/dist" ]; then
    sudo chown -R $USER:caddy $PROJECT_DIR/frontend/dist
    sudo chmod -R 755 $PROJECT_DIR/frontend/dist
    echo -e "${GREEN}✅ 前端文件权限设置完成${NC}"
else
    echo -e "${YELLOW}⚠️  前端dist目录不存在，可能需要重新构建${NC}"
fi

echo -e "${YELLOW}🔧 设置后端文件权限...${NC}"

# 设置后端目录权限
sudo chown -R $USER:$USER $PROJECT_DIR/backend

# 创建并设置uploads目录权限
sudo mkdir -p $PROJECT_DIR/backend/uploads
sudo chmod -R 775 $PROJECT_DIR/backend/uploads

# 设置Python虚拟环境权限
if [ -d "$PROJECT_DIR/backend/venv" ]; then
    sudo chown -R $USER:$USER $PROJECT_DIR/backend/venv
fi

echo -e "${GREEN}✅ 后端文件权限设置完成${NC}"

echo -e "${YELLOW}⚙️  设置Caddy配置权限...${NC}"

# 设置Caddy配置文件权限
if [ -f "/etc/caddy/Caddyfile" ]; then
    sudo chown caddy:caddy /etc/caddy/Caddyfile
    sudo chmod 644 /etc/caddy/Caddyfile
    echo -e "${GREEN}✅ Caddy配置文件权限设置完成${NC}"
else
    echo -e "${YELLOW}⚠️  Caddy配置文件不存在${NC}"
fi

echo -e "${YELLOW}📝 设置日志目录权限...${NC}"

# 创建并设置日志目录权限
sudo mkdir -p /var/log/caddy
sudo chown -R caddy:caddy /var/log/caddy
sudo chmod -R 755 /var/log/caddy

echo -e "${GREEN}✅ 日志目录权限设置完成${NC}"

echo -e "${YELLOW}🔍 验证权限设置...${NC}"

# 验证关键文件权限
echo "项目目录权限:"
ls -la /var/www/ | grep memory-brush

if [ -d "$PROJECT_DIR/frontend/dist" ]; then
    echo "前端文件权限:"
    ls -la $PROJECT_DIR/frontend/dist | head -5
fi

echo "后端目录权限:"
ls -la $PROJECT_DIR/backend | head -5

echo "Caddy配置权限:"
ls -la /etc/caddy/Caddyfile 2>/dev/null || echo "配置文件不存在"

echo "日志目录权限:"
ls -la /var/log/ | grep caddy

echo -e "${YELLOW}🔄 重启Caddy服务...${NC}"

# 重启Caddy服务以应用权限更改
if sudo systemctl restart caddy; then
    echo -e "${GREEN}✅ Caddy服务重启成功${NC}"
else
    echo -e "${RED}❌ Caddy服务重启失败${NC}"
    echo -e "${YELLOW}查看错误日志: sudo journalctl -u caddy -n 20${NC}"
fi

echo -e "${YELLOW}🧪 测试文件访问...${NC}"

# 测试Caddy是否能访问静态文件
if [ -f "$PROJECT_DIR/frontend/dist/index.html" ]; then
    if sudo -u caddy test -r "$PROJECT_DIR/frontend/dist/index.html"; then
        echo -e "${GREEN}✅ Caddy可以读取前端文件${NC}"
    else
        echo -e "${RED}❌ Caddy无法读取前端文件${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  前端index.html文件不存在${NC}"
fi

echo ""
echo -e "${GREEN}🎉 权限修复完成！${NC}"
echo ""
echo -e "${YELLOW}📋 权限总结：${NC}"
echo "  - 项目目录: $USER:caddy (755)"
echo "  - 前端文件: $USER:caddy (755)"
echo "  - 后端文件: $USER:$USER (755)"
echo "  - 上传目录: $USER:$USER (775)"
echo "  - Caddy配置: caddy:caddy (644)"
echo "  - 日志目录: caddy:caddy (755)"
echo ""
echo -e "${YELLOW}💡 如果仍有问题，请检查：${NC}"
echo "  1. SELinux状态: getenforce"
echo "  2. Caddy服务状态: sudo systemctl status caddy"
echo "  3. 详细日志: sudo journalctl -u caddy -f"
