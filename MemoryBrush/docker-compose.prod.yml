version: '3.8'

services:
  # 前端服务 - 生产环境
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
    environment:
      - VITE_API_URL=http://YOUR_SERVER_IP:8000
    depends_on:
      - backend
    networks:
      - memory-brush-network
    restart: unless-stopped

  # 后端服务 - 生产环境
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/static:/app/static
      - ./backend/logs:/app/logs
      - ./backend/memory_brush.db:/app/memory_brush.db
    environment:
      - DEBUG=False
      - DATABASE_URL=sqlite:///./memory_brush.db
      - SECRET_KEY=YOUR_PRODUCTION_SECRET_KEY_CHANGE_THIS
      - ALLOWED_HOSTS=http://YOUR_SERVER_IP,http://YOUR_DOMAIN.com
    networks:
      - memory-brush-network
    restart: unless-stopped

networks:
  memory-brush-network:
    driver: bridge
