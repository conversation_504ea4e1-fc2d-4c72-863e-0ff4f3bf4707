# 🔐 MemoryBrush 权限问题排查指南

## 🎯 权限配置概述

### Caddy服务配置
Caddy以 `caddy` 用户和组运行：
```ini
[Service]
User=caddy
Group=caddy
```

### 正确的权限配置
```
/var/www/memory-brush/          → $USER:caddy (755)
├── frontend/dist/              → $USER:caddy (755)
├── backend/                    → $USER:$USER (755)
│   ├── uploads/                → $USER:$USER (775)
│   └── venv/                   → $USER:$USER (755)
/etc/caddy/Caddyfile           → caddy:caddy (644)
/var/log/caddy/                → caddy:caddy (755)
```

## 🔍 常见权限问题

### 1. 静态文件无法访问
**症状**: 403 Forbidden错误，静态资源加载失败

**原因**: Caddy无法读取前端文件

**解决方案**:
```bash
# 检查前端文件权限
ls -la /var/www/memory-brush/frontend/dist/

# 修复权限
sudo chown -R $USER:caddy /var/www/memory-brush/frontend/dist/
sudo chmod -R 755 /var/www/memory-brush/frontend/dist/

# 测试Caddy是否能读取
sudo -u caddy test -r /var/www/memory-brush/frontend/dist/index.html
```

### 2. Caddy配置文件权限错误
**症状**: Caddy启动失败，配置加载错误

**原因**: Caddy无法读取配置文件

**解决方案**:
```bash
# 检查配置文件权限
ls -la /etc/caddy/Caddyfile

# 修复权限
sudo chown caddy:caddy /etc/caddy/Caddyfile
sudo chmod 644 /etc/caddy/Caddyfile

# 验证配置
sudo caddy validate --config /etc/caddy/Caddyfile
```

### 3. 日志写入权限问题
**症状**: 日志无法写入，Caddy运行异常

**原因**: Caddy无法写入日志目录

**解决方案**:
```bash
# 检查日志目录权限
ls -la /var/log/caddy/

# 修复权限
sudo mkdir -p /var/log/caddy
sudo chown -R caddy:caddy /var/log/caddy
sudo chmod -R 755 /var/log/caddy
```

### 4. 后端上传文件权限问题
**症状**: 文件上传失败

**原因**: 后端无法写入uploads目录

**解决方案**:
```bash
# 检查uploads目录权限
ls -la /var/www/memory-brush/backend/uploads/

# 修复权限
sudo mkdir -p /var/www/memory-brush/backend/uploads
sudo chown -R $USER:$USER /var/www/memory-brush/backend/uploads
sudo chmod -R 775 /var/www/memory-brush/backend/uploads
```

## 🛠️ 权限诊断工具

### 一键权限修复
```bash
# 运行权限修复脚本
./fix-permissions.sh
```

### 手动权限检查
```bash
# 检查关键目录权限
echo "=== 项目目录权限 ==="
ls -la /var/www/ | grep memory-brush

echo "=== 前端文件权限 ==="
ls -la /var/www/memory-brush/frontend/dist/ | head -5

echo "=== 后端目录权限 ==="
ls -la /var/www/memory-brush/backend/ | head -5

echo "=== Caddy配置权限 ==="
ls -la /etc/caddy/Caddyfile

echo "=== 日志目录权限 ==="
ls -la /var/log/ | grep caddy
```

### 权限测试
```bash
# 测试Caddy用户权限
echo "=== 测试Caddy读取权限 ==="
sudo -u caddy test -r /var/www/memory-brush/frontend/dist/index.html && echo "✅ 可读取" || echo "❌ 无法读取"

echo "=== 测试日志写入权限 ==="
sudo -u caddy test -w /var/log/caddy && echo "✅ 可写入" || echo "❌ 无法写入"

echo "=== 测试配置文件权限 ==="
sudo -u caddy test -r /etc/caddy/Caddyfile && echo "✅ 可读取" || echo "❌ 无法读取"
```

## 🔧 高级权限问题

### SELinux相关问题
如果启用了SELinux，可能需要额外配置：

```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，可能需要设置SELinux上下文
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_read_user_content 1

# 设置文件上下文
sudo restorecon -R /var/www/memory-brush/
sudo restorecon -R /etc/caddy/
sudo restorecon -R /var/log/caddy/
```

### AppArmor相关问题
Ubuntu系统可能有AppArmor限制：

```bash
# 检查AppArmor状态
sudo aa-status | grep caddy

# 如果有限制，可以临时禁用
sudo aa-complain /usr/bin/caddy

# 或者编辑AppArmor配置
sudo nano /etc/apparmor.d/usr.bin.caddy
```

### 文件系统权限问题
某些文件系统挂载选项可能影响权限：

```bash
# 检查挂载选项
mount | grep /var/www

# 确保没有noexec等限制选项
```

## 📊 权限监控

### 定期权限检查脚本
```bash
#!/bin/bash
# 保存为 check-permissions.sh

echo "=== MemoryBrush 权限检查 $(date) ==="

# 检查关键文件权限
files=(
    "/var/www/memory-brush"
    "/var/www/memory-brush/frontend/dist"
    "/etc/caddy/Caddyfile"
    "/var/log/caddy"
)

for file in "${files[@]}"; do
    if [ -e "$file" ]; then
        echo "$file: $(ls -ld "$file" | awk '{print $1, $3, $4}')"
    else
        echo "$file: 不存在"
    fi
done

# 测试Caddy权限
echo "=== Caddy权限测试 ==="
sudo -u caddy test -r /var/www/memory-brush/frontend/dist/index.html && echo "前端文件: ✅" || echo "前端文件: ❌"
sudo -u caddy test -r /etc/caddy/Caddyfile && echo "配置文件: ✅" || echo "配置文件: ❌"
sudo -u caddy test -w /var/log/caddy && echo "日志目录: ✅" || echo "日志目录: ❌"
```

### 权限监控告警
```bash
# 添加到crontab，每小时检查一次
0 * * * * /path/to/check-permissions.sh >> /var/log/permission-check.log 2>&1
```

## 🆘 紧急修复

### 快速权限重置
```bash
#!/bin/bash
# 紧急权限重置脚本

echo "🚨 紧急权限重置..."

# 停止服务
sudo systemctl stop caddy

# 重置所有权限
sudo chown -R $USER:caddy /var/www/memory-brush
sudo chmod -R 755 /var/www/memory-brush
sudo chown -R $USER:$USER /var/www/memory-brush/backend
sudo chown caddy:caddy /etc/caddy/Caddyfile
sudo chmod 644 /etc/caddy/Caddyfile
sudo chown -R caddy:caddy /var/log/caddy

# 重启服务
sudo systemctl start caddy

echo "✅ 权限重置完成"
```

## 📞 技术支持

### 常用诊断命令
```bash
# 查看Caddy服务状态
sudo systemctl status caddy

# 查看详细错误日志
sudo journalctl -u caddy -n 50

# 检查Caddy配置
sudo caddy validate --config /etc/caddy/Caddyfile

# 测试文件权限
namei -l /var/www/memory-brush/frontend/dist/index.html
```

### 联系信息
如果权限问题仍然存在，请提供：
1. 系统信息：`uname -a`
2. 权限信息：`ls -la /var/www/memory-brush/`
3. Caddy日志：`sudo journalctl -u caddy -n 20`
4. SELinux状态：`getenforce`

记住：正确的权限配置是Web服务正常运行的基础！🔐
