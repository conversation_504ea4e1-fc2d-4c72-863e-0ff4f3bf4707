# 🚀 MemoryBrush 部署指南

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 10GB 可用空间
- **网络**: 开放端口 80 (前端) 和 8000 (后端API)

### 2. 安装 Docker 和 Docker Compose

#### Ubuntu/Debian:
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

#### CentOS/RHEL:
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🔧 配置部署

### 1. 上传项目文件
将整个 `MemoryBrush` 文件夹上传到服务器，例如：
```bash
# 使用scp上传
scp -r MemoryBrush/ user@your-server:/home/<USER>/

# 或使用git克隆
git clone your-repository-url
cd MemoryBrush
```

### 2. 修改配置文件

#### 修改 `docker-compose.prod.yml`:
```yaml
# 将 YOUR_SERVER_IP 替换为实际的服务器IP
environment:
  - VITE_API_URL=http://*************:8000  # 替换为实际IP

# 将 YOUR_PRODUCTION_SECRET_KEY 替换为安全的密钥
environment:
  - SECRET_KEY=your-very-secure-secret-key-here
```

#### 修改 `frontend/.env.production`:
```env
VITE_API_URL=http://*************:8000  # 替换为实际IP
```

### 3. 设置执行权限
```bash
chmod +x deploy.sh
```

## 🚀 开始部署

### 方法一：使用部署脚本（推荐）
```bash
./deploy.sh
```

### 方法二：手动部署
```bash
# 构建并启动服务
docker-compose -f docker-compose.prod.yml up --build -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查前端
curl http://localhost:80

# 检查后端API
curl http://localhost:8000/health
```

### 2. 访问应用
- **前端**: http://YOUR_SERVER_IP
- **后端API**: http://YOUR_SERVER_IP:8000

## 🛠️ 常用运维命令

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f backend
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 重启特定服务
docker-compose -f docker-compose.prod.yml restart frontend
```

### 停止服务
```bash
docker-compose -f docker-compose.prod.yml down
```

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新部署
./deploy.sh
```

### 备份数据
```bash
# 备份数据库
cp backend/memory_brush.db backup/memory_brush_$(date +%Y%m%d_%H%M%S).db

# 备份上传文件
tar -czf backup/uploads_$(date +%Y%m%d_%H%M%S).tar.gz backend/uploads/
```

## 🔒 安全建议

1. **防火墙配置**:
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 8000
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

2. **SSL证书** (可选):
   - 使用 Let's Encrypt 获取免费SSL证书
   - 配置 Nginx 反向代理

3. **定期备份**:
   - 设置定时任务备份数据库和上传文件
   - 监控磁盘空间使用情况

## 🐛 故障排除

### 常见问题

1. **端口被占用**:
```bash
# 查看端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :8000

# 停止占用端口的进程
sudo kill -9 PID
```

2. **Docker权限问题**:
```bash
# 将用户添加到docker组
sudo usermod -aG docker $USER
# 重新登录或执行
newgrp docker
```

3. **内存不足**:
```bash
# 查看内存使用
free -h
# 查看Docker容器资源使用
docker stats
```

4. **查看详细错误日志**:
```bash
# 查看容器详细日志
docker-compose -f docker-compose.prod.yml logs --tail=100 backend
```

## 📞 技术支持

如果遇到部署问题，请检查：
1. 服务器配置是否满足要求
2. Docker和Docker Compose是否正确安装
3. 网络端口是否正确开放
4. 配置文件中的IP地址是否正确
