# ✅ MemoryBrush 部署检查清单

## 📋 部署前准备

### 🌐 域名和DNS配置
- [ ] 域名 `www.mb.com` 已注册
- [ ] DNS A记录已添加：`www.mb.com` → `*******`
- [ ] DNS解析已生效（可通过 `nslookup www.mb.com` 验证）
- [ ] 域名备案已完成（如需要）

### 🖥️ 服务器准备
- [ ] 服务器IP确认：`*******`
- [ ] 操作系统：Ubuntu 20.04+ / Debian 10+ / CentOS 7+
- [ ] 内存：≥ 1GB（推荐 2GB+）
- [ ] 存储：≥ 5GB 可用空间
- [ ] SSH访问正常
- [ ] 具有sudo权限的非root用户

### 🔒 网络和安全
- [ ] 防火墙端口开放：
  - [ ] 端口 80 (HTTP重定向)
  - [ ] 端口 443 (HTTPS)
  - [ ] 端口 8000 (后端API，可选)
- [ ] 服务器时间同步正确
- [ ] 安全组/防火墙规则配置

## 🚀 部署过程

### 1. 文件上传
- [ ] 项目文件已上传到服务器
- [ ] 部署脚本有执行权限：`chmod +x deploy-direct.sh`

### 2. 运行部署
- [ ] 执行部署脚本：`./deploy-direct.sh`
- [ ] 观察部署过程无错误
- [ ] 等待SSL证书获取完成（1-2分钟）

### 3. 服务检查
- [ ] 后端服务运行正常：`sudo systemctl status memory-brush-backend`
- [ ] Caddy服务运行正常：`sudo systemctl status caddy`
- [ ] 端口监听正常：
  - [ ] `sudo netstat -tlnp | grep :80`
  - [ ] `sudo netstat -tlnp | grep :443`
  - [ ] `sudo netstat -tlnp | grep :8000`

## 🔍 部署验证

### 🌐 网络访问测试
- [ ] HTTPS访问：`curl -I https://www.mb.com`
- [ ] HTTP重定向：`curl -I http://www.mb.com` (应返回301/302)
- [ ] IP重定向：`curl -I http://*******` (应重定向到域名)
- [ ] 后端API：`curl -I http://*******:8000`

### 🔒 SSL证书验证
- [ ] SSL证书有效：`openssl s_client -connect www.mb.com:443 -servername www.mb.com`
- [ ] 证书颁发者：Let's Encrypt
- [ ] 证书有效期：≥ 85天
- [ ] SSL Labs测试：A级评分（可选）

### 🎯 功能测试
- [ ] 前端页面正常加载
- [ ] 静态资源加载正常（CSS、JS、图片）
- [ ] SPA路由工作正常
- [ ] API接口响应正常
- [ ] 用户交互功能正常

## 📊 性能和监控

### 📈 性能检查
- [ ] 页面加载时间 < 3秒
- [ ] 静态资源缓存生效
- [ ] HTTP/2协议启用
- [ ] Gzip压缩启用

### 📝 日志配置
- [ ] Caddy访问日志：`/var/log/caddy/memory-brush.log`
- [ ] 后端服务日志：`sudo journalctl -u memory-brush-backend`
- [ ] 系统日志正常
- [ ] 日志轮转配置

### 🔔 监控设置
- [ ] 服务状态监控
- [ ] SSL证书到期监控
- [ ] 磁盘空间监控
- [ ] 内存使用监控

## 🛠️ 运维准备

### 📚 文档和命令
- [ ] 常用运维命令已记录
- [ ] 故障排除文档已准备
- [ ] 备份策略已制定
- [ ] 更新流程已确定

### 🔧 维护工具
- [ ] 服务重启脚本
- [ ] 日志查看命令
- [ ] 配置验证命令
- [ ] 备份恢复脚本

## 🎉 部署完成确认

### ✅ 最终检查
- [ ] 所有服务正常运行
- [ ] HTTPS访问无问题
- [ ] 应用功能完整
- [ ] 性能表现良好
- [ ] 监控和日志正常
- [ ] 文档和工具齐全

### 📞 交付信息
- **主要访问地址**: https://www.mb.com
- **管理后台**: https://www.mb.com/admin (如有)
- **服务器IP**: *******
- **部署时间**: ___________
- **负责人**: ___________

## 🆘 常见问题快速解决

### DNS问题
```bash
# 检查DNS解析
nslookup www.mb.com
dig www.mb.com @*******
```

### SSL证书问题
```bash
# 重新获取证书
sudo systemctl stop caddy
sudo rm -rf /var/lib/caddy/.local/share/caddy/certificates/
sudo systemctl start caddy
```

### 服务问题
```bash
# 重启所有服务
sudo systemctl restart memory-brush-backend caddy

# 查看详细日志
sudo journalctl -u caddy -f
sudo journalctl -u memory-brush-backend -f
```

### 防火墙问题
```bash
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --list-all
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

---

**部署完成后，请保存此检查清单作为运维参考！** 🎯
