version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - memory-brush-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/static:/app/static
      - ./backend/logs:/app/logs
    environment:
      - DEBUG=True
      - DATABASE_URL=sqlite:///./memory_brush.db
      - SECRET_KEY=memory-brush-secret-key-change-in-production
      - ALLOWED_HOSTS=http://localhost:5173,http://127.0.0.1:5173
    networks:
      - memory-brush-network

  # 数据库服务（可选，如果使用PostgreSQL）
  # database:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: memory_brush
  #     POSTGRES_USER: memory_brush_user
  #     POSTGRES_PASSWORD: memory_brush_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - memory-brush-network

  # Redis服务（可选，用于缓存）
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - memory-brush-network

networks:
  memory-brush-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
